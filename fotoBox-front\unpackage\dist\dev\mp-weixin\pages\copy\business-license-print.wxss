/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.business-license-print {
  background-color: #f5f5f5;
  min-height: 100vh;
}
.business-license-print__header {
  background: linear-gradient(135deg, #FFE4B5 0%, #FFF8DC 100%);
  padding: 20rpx;
  text-align: center;
  margin: 0;
}
.business-license-print__tip {
  font-size: 28rpx;
  color: #D2691E;
  font-weight: 500;
}
.business-license-print__license-container {
  background-color: #F5E6A8;
  margin: 20rpx;
  border-radius: 16rpx;
  padding: 40rpx 30rpx;
  position: relative;
  min-height: 800rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
}
.business-license-print__license-container:active {
  opacity: 0.9;
}
.business-license-print__title {
  text-align: center;
  margin-bottom: 50rpx;
}
.business-license-print__title-text {
  font-size: 48rpx;
  font-weight: bold;
  color: #FFA500;
  text-shadow: 2rpx 2rpx 4rpx rgba(0, 0, 0, 0.1);
}
.business-license-print__table {
  margin-bottom: 40rpx;
  width: 100%;
}
.business-license-print__row {
  display: flex;
  margin-bottom: 12rpx;
}
.business-license-print__row:last-child {
  margin-bottom: 0;
}
.business-license-print__cell {
  flex: 1;
  height: 60rpx;
  background-color: #FFF8DC;
  border: 2rpx solid #E6D35A;
  margin-right: 12rpx;
  border-radius: 6rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}
.business-license-print__cell:last-child {
  margin-right: 0;
}
.business-license-print__cell-text {
  font-size: 24rpx;
  color: transparent;
}
.business-license-print__photo-area {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 20rpx;
  z-index: 10;
}
.business-license-print__camera-circle {
  width: 140rpx;
  height: 140rpx;
  background: linear-gradient(135deg, rgba(0, 0, 0, 0.8) 0%, rgba(60, 60, 60, 0.8) 100%);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 6rpx 20rpx rgba(0, 0, 0, 0.4);
  border: 4rpx solid rgba(255, 255, 255, 0.3);
}
.business-license-print__camera-circle:active {
  transform: scale(0.95);
  transition: transform 0.1s ease;
}
.business-license-print__photo-text {
  font-size: 32rpx;
  color: #333333;
  font-weight: bold;
  text-shadow: 0 2rpx 4rpx rgba(255, 255, 255, 0.8);
  background: rgba(255, 255, 255, 0.9);
  padding: 8rpx 16rpx;
  border-radius: 20rpx;
  border: 2rpx solid #E6D35A;
}

/* 适配不同屏幕尺寸 */
@media screen and (max-width: 375px) {
.business-license-print__license-container {
    margin: 15rpx;
    padding: 30rpx 20rpx;
    min-height: 700rpx;
}
.business-license-print__title-text {
    font-size: 40rpx;
}
.business-license-print__camera-circle {
    width: 100rpx;
    height: 100rpx;
}
.business-license-print__photo-text {
    font-size: 28rpx;
}
.business-license-print__cell {
    height: 45rpx;
}
}