<template>
	<view class="f-header" :style="{ background: bgColor }">
		<!-- 顶部安全区域适配 -->
		<view class="f-header__status-bar" :style="{ height: statusBarHeight }"></view>
		
		<view class="f-header__content">
		<view class="f-header__left">
			<view class="f-header__location" @click="onLocationClick">
				<uni-icons type="location" color="#FFFFFF" size="20"></uni-icons>
				<text class="f-header__location-text">{{ location }}</text>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
	export default {
		name: 'FHeader',
		props: {
			location: {
				type: String,
				default: '[000000]未选取件地点！'
			},
			bgColor: {
				type: String,
				default: '#FFA500' // 橙色背景
			}
		},
		data() {
			return {
				statusBarHeight: '40rpx'
			};
		},
		created() {
			// 获取状态栏高度
			this.getStatusBarHeight();
		},
		methods: {
			// 获取状态栏高度
			getStatusBarHeight() {
				try {
					// 首先尝试从存储中获取
					const storedHeight = uni.getStorageSync('statusBarHeight');
					if (storedHeight) {
						this.statusBarHeight = storedHeight;
						return;
					}
					
					// 如果没有，则重新获取
					const systemInfo = uni.getSystemInfoSync();
					this.statusBarHeight = systemInfo.statusBarHeight + 'px';
					
				} catch (e) {
					// 发生错误时使用默认值
					console.error('获取状态栏高度失败', e);
					this.statusBarHeight = '40rpx';
				}
			},
			onLocationClick() {
				this.$emit('location-click');
				
				// 跳转到网点页面
				uni.switchTab({
					url: '/pages/location/index',
					fail: () => {
						uni.navigateTo({
							url: '/pages/location/index'
						});
					}
				});
			}
		}
	}
</script>

<style lang="scss">
	.f-header {
		display: flex;
		flex-direction: column;
		color: #fff;
		position: relative;
		background-color: #FFA500;
		box-sizing: border-box;
		
		&__status-bar {
			width: 100%;
			/* 高度由js动态设置 */
		}
		
		&__content {
			display: flex;
			justify-content: space-between;
			align-items: center;
			padding: 16rpx 30rpx;
			height: 84rpx;
			box-sizing: border-box;
		}
		
		&__left {
			display: flex;
			align-items: center;
		}
		
		&__location {
			display: flex;
			align-items: center;
			font-size: 28rpx;
			
			.uni-icons {
				margin-right: 8rpx;
			}
		}
		
		&__location-text {
			max-width: 400rpx;
			overflow: hidden;
			text-overflow: ellipsis;
			white-space: nowrap;
		}
	}
</style> 