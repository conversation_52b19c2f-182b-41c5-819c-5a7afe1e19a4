<template>
	<f-page-layout 
		title="拍照打印" 
		fallback-path="/pages/document/index"
		@back-click="onBackClick"
	>
		<view class="photo-print">
			<view class="photo-print__container">
				<!-- 第一行：A4文档 和 身份证/居住证 -->
				<view class="photo-print__row">
					<!-- A4文档 -->
					<view class="photo-print__card" @click="handleA4Print">
						<view class="photo-print__card-icon photo-print__card-icon--a4">
							<uni-icons type="paperclip" color="#FFFFFF" size="22"></uni-icons>
						</view>
						<view class="photo-print__card-text">
							<text class="photo-print__card-title">A4文档</text>
						</view>
					</view>
					
					<!-- 身份证/居住证 -->
					<view class="photo-print__card" @click="handleIdCardPrint">
						<view class="photo-print__card-icon photo-print__card-icon--id">
							<uni-icons type="person" color="#FFFFFF" size="22"></uni-icons>
						</view>
						<view class="photo-print__card-text">
							<view>
								<text class="photo-print__card-title">身份证</text>
							</view>
							<view>
								<text class="photo-print__card-title">居住证</text>
							</view>
						</view>
					</view>
				</view>
				
				<!-- 第二行：户口本 和 营业执照 -->
				<view class="photo-print__row">
					<!-- 户口本 -->
					<view class="photo-print__card" @click="handleHouseholdPrint">
						<view class="photo-print__card-icon photo-print__card-icon--household">
							<uni-icons type="list" color="#FFFFFF" size="22"></uni-icons>
						</view>
						<view class="photo-print__card-text">
							<text class="photo-print__card-title">户口本</text>
						</view>
					</view>
					
					<!-- 营业执照 -->
					<view class="photo-print__card" @click="handleBusinessLicensePrint">
						<view class="photo-print__card-icon photo-print__card-icon--business">
							<uni-icons type="shop" color="#FFFFFF" size="22"></uni-icons>
						</view>
						<view class="photo-print__card-text">
							<text class="photo-print__card-title">营业执照</text>
						</view>
					</view>
				</view>
				
				<!-- 第三行：驾驶证/行驶证 和 银行卡/社保卡 -->
				<view class="photo-print__row">
					<!-- 驾驶证/行驶证 -->
					<view class="photo-print__card" @click="handleDrivingLicensePrint">
						<view class="photo-print__card-icon photo-print__card-icon--driving">
							<uni-icons type="navigate" color="#FFFFFF" size="22"></uni-icons>
						</view>
						<view class="photo-print__card-text">
							<view>
								<text class="photo-print__card-title">驾驶证</text>
							</view>
							<view>
								<text class="photo-print__card-title">行驶证</text>
							</view>
						</view>
					</view>
					
					<!-- 银行卡/社保卡 -->
					<view class="photo-print__card" @click="handleBankCardPrint">
						<view class="photo-print__card-icon photo-print__card-icon--card">
							<uni-icons type="wallet" color="#FFFFFF" size="22"></uni-icons>
						</view>
						<view class="photo-print__card-text">
							<view>
								<text class="photo-print__card-title">银行卡</text>
							</view>
							<view>
								<text class="photo-print__card-title">社保卡</text>
							</view>
						</view>
					</view>
				</view>
			</view>
		</view>
	</f-page-layout>
</template>

<script>
	export default {
		data() {
			return {
				// 数据
			};
		},
		methods: {
			// 返回上一页
			onBackClick() {
				console.log('返回上一页');
			},
			// 处理A4文档打印
			handleA4Print() {
				console.log('点击了A4文档');
				// 跳转到复印服务的A4文档打印页面
				uni.navigateTo({
					url: '/pages/copy/a4-print'
				});
			},
			// 处理身份证/居住证打印
			handleIdCardPrint() {
				console.log('点击了身份证/居住证');
				uni.navigateTo({
					url: '/pages/copy/id-card-print'
				});
			},
			// 处理户口本打印
			handleHouseholdPrint() {
				console.log('点击了户口本');
				uni.navigateTo({
					url: '/pages/copy/household-print'
				});
			},
			// 处理营业执照打印
			handleBusinessLicensePrint() {
				console.log('点击了营业执照');
				uni.navigateTo({
					url: '/pages/copy/business-license-print'
				});
			},
			// 处理驾驶证/行驶证打印
			handleDrivingLicensePrint() {
				console.log('点击了驾驶证/行驶证');
				uni.navigateTo({
					url: '/pages/copy/driving-license-print'
				});
			},
			// 处理银行卡/社保卡打印
			handleBankCardPrint() {
				console.log('点击了银行卡/社保卡');
				uni.navigateTo({
					url: '/pages/copy/bank-card-print'
				});
			}
		}
	}
</script>

<style lang="scss">
	.photo-print {
		background-color: #f5f5f5;
		min-height: 100%;
		padding: 20rpx;
		
		&__container {
			display: flex;
			flex-direction: column;
			gap: 20rpx;
		}
		
		&__row {
			display: flex;
			gap: 20rpx;
		}
		
		&__card {
			flex: 1;
			background-color: #ffffff;
			border-radius: 12rpx;
			display: flex;
			flex-direction: row;
			align-items: center;
			padding: 25rpx 20rpx;
			box-shadow: 0 1rpx 4rpx rgba(0, 0, 0, 0.05);
			min-height: 80rpx;
		}
		
		&__card-icon {
			width: 70rpx;
			height: 70rpx;
			border-radius: 10rpx;
			display: flex;
			align-items: center;
			justify-content: center;
			margin-right: 25rpx;
			flex-shrink: 0;
			
			&--a4 {
				background-color: #ff9800;
			}
			
			&--id {
				background-color: #3f51b5;
			}
			
			&--household {
				background-color: #e53935;
			}
			
			&--business {
				background-color: #4caf50;
			}
			
			&--driving {
				background-color: #2196f3;
			}
			
			&--card {
				background-color: #e91e63;
			}
		}
		
		&__card-text {
			display: flex;
			flex-direction: column;
			flex: 1;
			justify-content: center;
			
			view {
				margin: 2rpx 0;
			}
		}
		
		&__card-title {
			font-size: 30rpx;
			color: #333;
			font-weight: bold;
			line-height: 1.4;
		}
	}
</style> 
