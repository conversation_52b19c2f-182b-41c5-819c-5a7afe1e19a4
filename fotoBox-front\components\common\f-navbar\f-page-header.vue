<template>
	<view class="f-page-header" :style="{ background: bgColor }">
		<!-- 顶部安全区域适配 -->
		<view class="f-page-header__status-bar" :style="{ height: statusBarHeight }"></view>
		
		<view class="f-page-header__content">
		<view class="f-page-header__left">
			<view class="f-page-header__back" @click="onBackClick">
				<uni-icons type="left" color="#FFFFFF" size="24"></uni-icons>
			</view>
		</view>
		<view class="f-page-header__title">{{ title }}</view>
		<view class="f-page-header__right"></view>
		</view>
	</view>
</template>

<script>
	export default {
		name: 'FPageHeader',
		props: {
			title: {
				type: String,
				default: '页面标题'
			},
			bgColor: {
				type: String,
				default: '#FFA500' // 橙色背景
			},
			// 自定义返回路径，如果提供，则优先使用
			customBackPath: {
				type: String,
				default: ''
			},
			// 备用路径，当返回失败时使用
			fallbackPath: {
				type: String,
				default: '/pages/index/index'
			},
			// 返回时要返回的层级数
			delta: {
				type: Number,
				default: 1
			}
		},
		data() {
			return {
				// tabBar页面路径列表
				tabBarPages: ['/pages/index/index', '/pages/location/index'],
				statusBarHeight: '40rpx'
			};
		},
		created() {
			// 获取状态栏高度
			this.getStatusBarHeight();
		},
		methods: {
			// 获取状态栏高度
			getStatusBarHeight() {
				try {
					// 首先尝试从存储中获取
					const storedHeight = uni.getStorageSync('statusBarHeight');
					if (storedHeight) {
						this.statusBarHeight = storedHeight;
						return;
					}
					
					// 如果没有，则重新获取
					const systemInfo = uni.getSystemInfoSync();
					this.statusBarHeight = systemInfo.statusBarHeight + 'px';
					
				} catch (e) {
					// 发生错误时使用默认值
					console.error('获取状态栏高度失败', e);
					this.statusBarHeight = '40rpx';
				}
			},
			onBackClick() {
				// 发出事件，让父组件有机会处理
				this.$emit('back-click');
				
				// 如果有自定义返回路径，直接跳转
				if (this.customBackPath) {
					if (this.customBackPath.indexOf('/pages/') === 0) {
						// 判断是否是 tabBar 页面
						if (this.tabBarPages.includes(this.customBackPath)) {
							uni.switchTab({
								url: this.customBackPath
							});
						} else {
							uni.navigateTo({
								url: this.customBackPath
							});
						}
					}
					return;
				}
				
				// 尝试返回上一页
				uni.navigateBack({
					delta: this.delta,
					fail: (err) => {
						console.log('返回失败，跳转到备用路径', err);
						// 如果返回失败（例如，历史栈中没有页面），则导航到备用路径
						if (this.fallbackPath) {
							console.log('使用备用路径:', this.fallbackPath);
							// 判断是否是 tabBar 页面
							if (this.tabBarPages.includes(this.fallbackPath)) {
								uni.switchTab({
									url: this.fallbackPath
								});
							} else {
								uni.navigateTo({
									url: this.fallbackPath
								});
							}
						}
					}
				});
			}
		}
	}
</script>

<style lang="scss">
	.f-page-header {
		display: flex;
		flex-direction: column;
		color: #fff;
		position: relative;
		box-sizing: border-box;
		
		&__status-bar {
			width: 100%;
			/* 高度由js动态设置 */
		}
		
		&__content {
			display: flex;
			justify-content: space-between;
			align-items: center;
			padding: 0 30rpx;
			height: 84rpx;
			box-sizing: border-box;
		}
		
		&__left {
			width: 60rpx;
			display: flex;
			align-items: center;
		}
		
		&__back {
			display: flex;
			align-items: center;
			font-size: 34rpx;
			
			&:active {
				opacity: 0.7; // 点击时的反馈效果
			}
		}
		
		&__title {
			font-size: 32rpx;
			font-weight: bold;
			text-align: center;
			flex: 1;
		}
		
		&__right {
			width: 60rpx;
		}
	}
</style> 