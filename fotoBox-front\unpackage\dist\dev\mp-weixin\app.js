"use strict";
Object.defineProperty(exports, Symbol.toStringTag, { value: "Module" });
const common_vendor = require("./common/vendor.js");
const store_index = require("./store/index.js");
const api_index = require("./api/index.js");
if (!Math) {
  "./pages/index/index.js";
  "./pages/location/index.js";
  "./pages/document/index.js";
  "./pages/photo/index.js";
  "./pages/photo/six-inch-photo.js";
  "./pages/photo/five-inch-photo.js";
  "./pages/id-photo/index.js";
  "./pages/copy/index.js";
  "./pages/service/index.js";
  "./pages/user/index.js";
  "./pages/document/photo-print.js";
  "./pages/id-photo/search.js";
  "./pages/document/a4-print.js";
  "./pages/document/id-card-print.js";
  "./pages/document/household-print.js";
  "./pages/document/business-license-print.js";
  "./pages/document/driving-license-print.js";
  "./pages/login/index.js";
  "./pages/document/wechat-files.js";
  "./pages/document/print-settings.js";
  "./pages/document/photo-album.js";
  "./pages/order/detail.js";
  "./pages/document/local-files.js";
  "./pages/order/index.js";
  "./pages/document/bank-card-print.js";
  "./pages/copy/bank-card-print.js";
  "./pages/copy/a4-print.js";
  "./pages/copy/id-card-print.js";
  "./pages/copy/household-print.js";
  "./pages/copy/business-license-print.js";
  "./pages/copy/driving-license-print.js";
}
const _sfc_main = {
  onLaunch: function() {
    common_vendor.index.__f__("log", "at App.vue:4", "App Launch");
    this.setStatusBarHeight();
  },
  onShow: function() {
    common_vendor.index.__f__("log", "at App.vue:10", "App Show");
  },
  onHide: function() {
    common_vendor.index.__f__("log", "at App.vue:13", "App Hide");
  },
  methods: {
    // 设置状态栏高度
    setStatusBarHeight() {
      const systemInfo = common_vendor.index.getSystemInfoSync();
      const statusBarHeight = systemInfo.statusBarHeight + "px";
      common_vendor.index.__f__("log", "at App.vue:23", "状态栏高度:", statusBarHeight);
      document.documentElement.style.setProperty("--status-bar-height", statusBarHeight);
      common_vendor.index.setStorageSync("statusBarHeight", statusBarHeight);
    }
  }
};
const uniIcons = () => "./uni_modules/uni-icons/components/uni-icons/uni-icons.js";
const FHeader = () => "./components/common/f-navbar/f-header.js";
const FTabbar = () => "./components/common/f-navbar/f-tabbar.js";
const FPageHeader = () => "./components/common/f-navbar/f-page-header.js";
const FServiceCard = () => "./components/common/f-service-card/f-service-card.js";
const FTabLayout = () => "./components/common/f-layout/f-tab-layout.js";
const FPageLayout = () => "./components/common/f-layout/f-page-layout.js";
function createApp() {
  const app = common_vendor.createSSRApp(_sfc_main);
  app.use(store_index.store);
  app.component("uni-icons", uniIcons);
  app.component("f-header", FHeader);
  app.component("f-tabbar", FTabbar);
  app.component("f-page-header", FPageHeader);
  app.component("f-service-card", FServiceCard);
  app.component("f-tab-layout", FTabLayout);
  app.component("f-page-layout", FPageLayout);
  app.config.globalProperties.$api = api_index.api;
  return {
    app
  };
}
createApp().app.mount("#app");
exports.createApp = createApp;
//# sourceMappingURL=../.sourcemap/mp-weixin/app.js.map
