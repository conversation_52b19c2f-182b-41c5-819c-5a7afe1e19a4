"use strict";
const common_vendor = require("../../common/vendor.js");
const _sfc_main = {
  data() {
    return {
      showSheet: false
    };
  },
  methods: {
    // 返回上一页
    onBackClick() {
      common_vendor.index.navigateBack({
        delta: 1
      });
    },
    // 显示操作菜单
    showActionSheet() {
      this.showSheet = true;
    },
    // 隐藏操作菜单
    hideActionSheet() {
      this.showSheet = false;
    },
    // 拍摄文档
    takePhoto() {
      this.hideActionSheet();
      common_vendor.index.chooseImage({
        count: 1,
        sourceType: ["camera"],
        success: (res) => {
          common_vendor.index.__f__("log", "at pages/document/a4-print.vue:81", "拍摄成功:", res.tempFilePaths);
          this.previewPhoto(res.tempFilePaths[0]);
        },
        fail: () => {
          common_vendor.index.showToast({
            title: "拍摄取消",
            icon: "none"
          });
        }
      });
    },
    // 从相册选择
    chooseFromAlbum() {
      this.hideActionSheet();
      common_vendor.index.chooseImage({
        count: 1,
        sourceType: ["album"],
        success: (res) => {
          common_vendor.index.__f__("log", "at pages/document/a4-print.vue:101", "选择成功:", res.tempFilePaths);
          this.previewPhoto(res.tempFilePaths[0]);
        },
        fail: () => {
          common_vendor.index.showToast({
            title: "选择取消",
            icon: "none"
          });
        }
      });
    },
    // 预览照片
    previewPhoto(path) {
      common_vendor.index.showToast({
        title: "文档拍摄成功",
        icon: "success"
      });
    }
  }
};
if (!Array) {
  const _easycom_uni_icons2 = common_vendor.resolveComponent("uni-icons");
  const _component_f_page_layout = common_vendor.resolveComponent("f-page-layout");
  (_easycom_uni_icons2 + _component_f_page_layout)();
}
const _easycom_uni_icons = () => "../../uni_modules/uni-icons/components/uni-icons/uni-icons.js";
if (!Math) {
  _easycom_uni_icons();
}
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return common_vendor.e({
    a: common_vendor.f(15, (item, index, i0) => {
      return {
        a: index
      };
    }),
    b: common_vendor.p({
      type: "camera",
      color: "#FFFFFF",
      size: "32"
    }),
    c: common_vendor.o((...args) => $options.showActionSheet && $options.showActionSheet(...args)),
    d: $data.showSheet
  }, $data.showSheet ? {
    e: common_vendor.o((...args) => $options.takePhoto && $options.takePhoto(...args)),
    f: common_vendor.o((...args) => $options.chooseFromAlbum && $options.chooseFromAlbum(...args)),
    g: common_vendor.o((...args) => $options.hideActionSheet && $options.hideActionSheet(...args)),
    h: common_vendor.o(() => {
    }),
    i: common_vendor.o((...args) => $options.hideActionSheet && $options.hideActionSheet(...args))
  } : {}, {
    j: common_vendor.o($options.onBackClick),
    k: common_vendor.p({
      title: "A4文档拍照打印",
      ["fallback-path"]: "/pages/document/photo-print"
    })
  });
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages/document/a4-print.js.map
