{"version": 3, "file": "household-print.js", "sources": ["pages/copy/household-print.vue", "C:/Program Files/HBuilderX.4.66.2025051912/plugins/uniapp-cli-vite/uniPage:/cGFnZXMvY29weS9ob3VzZWhvbGQtcHJpbnQudnVl"], "sourcesContent": ["<template>\n    <f-page-layout \n        title=\"户口本打印\" \n        fallback-path=\"/pages/copy/index\"\n        @back-click=\"onBackClick\"\n    >\n        <view class=\"household-print\">\n            <!-- 顶部提示 -->\n            <view class=\"tip-banner\">\n                <text class=\"tip-text\">内容占照时需要与背景颜色尽量区分</text>\n            </view>\n            \n            <!-- 户主页 -->\n            <view class=\"page-card\" @click=\"showActionSheet('master')\">\n                <view class=\"page-header\">\n                    <text class=\"page-title\">户主页</text>\n                </view>\n\n                <view class=\"page-content\">\n                    <!-- 户口本户主页背景图片 -->\n                    <image\n                        v-if=\"masterPageImage\"\n                        class=\"household-print__page-image\"\n                        :src=\"masterPageImage\"\n                        mode=\"aspectFill\"\n                    />\n                    <!-- 无图片时的占位符 -->\n                    <view v-else class=\"household-print__page-placeholder\">\n                        <view class=\"household-print__placeholder-text\">户主页模板</view>\n                        <view class=\"household-print__placeholder-desc\">图片将在此处显示</view>\n                    </view>\n\n                    <!-- 相机图标覆盖层 -->\n                    <view class=\"household-print__camera-overlay\">\n                        <view class=\"household-print__camera\">\n                            <uni-icons type=\"camera\" color=\"#FFFFFF\" size=\"30\"></uni-icons>\n                        </view>\n                        <view class=\"household-print__text\">\n                            拍摄户主页\n                        </view>\n                    </view>\n                </view>\n            </view>\n            \n            <!-- 个人信息页 -->\n            <view class=\"page-card\" @click=\"showActionSheet('personal')\">\n                <view class=\"page-header\">\n                    <text class=\"page-title\">个人信息页</text>\n                </view>\n\n                <view class=\"page-content\">\n                    <!-- 户口本个人信息页背景图片 -->\n                    <image\n                        v-if=\"personalPageImage\"\n                        class=\"household-print__page-image\"\n                        :src=\"personalPageImage\"\n                        mode=\"aspectFill\"\n                    />\n                    <!-- 无图片时的占位符 -->\n                    <view v-else class=\"household-print__page-placeholder\">\n                        <view class=\"household-print__placeholder-text\">个人信息页模板</view>\n                        <view class=\"household-print__placeholder-desc\">图片将在此处显示</view>\n                    </view>\n\n                    <!-- 相机图标覆盖层 -->\n                    <view class=\"household-print__camera-overlay\">\n                        <view class=\"household-print__camera\">\n                            <uni-icons type=\"camera\" color=\"#FFFFFF\" size=\"30\"></uni-icons>\n                        </view>\n                        <view class=\"household-print__text\">\n                            拍摄个人信息页\n                        </view>\n                    </view>\n                </view>\n            </view>\n            \n            <!-- 底部操作按钮 -->\n            <view class=\"action-buttons\">\n                <button class=\"btn btn-secondary\" @click=\"resetPhotos\">重新拍照</button>\n                <button class=\"btn btn-primary\" @click=\"confirmPrint\" :disabled=\"!canPrint\">确认打印</button>\n            </view>\n        </view>\n    </f-page-layout>\n</template>\n\n<script>\nexport default {\n    data() {\n        return {\n            currentPage: '', // 当前拍摄的页面 'master' 或 'personal'\n            photos: {\n                master: '', // 户主页拍摄照片\n                personal: '' // 个人信息页拍摄照片\n            },\n            // 户口本模板图片 - 留空供后续引用\n            masterPageImage: '', // 户主页模板图片路径\n            personalPageImage: '' // 个人信息页模板图片路径\n        };\n    },\n    computed: {\n        // 检查是否可以打印（两张照片都已拍摄）\n        canPrint() {\n            return this.photos.master && this.photos.personal;\n        }\n    },\n    methods: {\n        // 返回上一页\n        onBackClick() {\n            uni.navigateBack();\n        },\n        \n        // 显示操作选择\n        showActionSheet(page) {\n            this.currentPage = page;\n            const pageText = page === 'master' ? '户主页' : '个人信息页';\n\n            uni.showActionSheet({\n                itemList: ['拍照', '从相册选择'],\n                success: (res) => {\n                    if (res.tapIndex === 0) {\n                        this.takePhoto();\n                    } else if (res.tapIndex === 1) {\n                        this.chooseFromAlbum();\n                    }\n                }\n            });\n        },\n\n        // 拍照\n        takePhoto() {\n            uni.chooseImage({\n                count: 1,\n                sourceType: ['camera'],\n                success: (res) => {\n                    this.handleImageSelected(res.tempFilePaths[0]);\n                },\n                fail: (err) => {\n                    console.error('拍照失败:', err);\n                    uni.showToast({\n                        title: '拍照失败',\n                        icon: 'none'\n                    });\n                }\n            });\n        },\n\n        // 从相册选择\n        chooseFromAlbum() {\n            uni.chooseImage({\n                count: 1,\n                sourceType: ['album'],\n                success: (res) => {\n                    this.handleImageSelected(res.tempFilePaths[0]);\n                },\n                fail: (err) => {\n                    console.error('选择图片失败:', err);\n                    uni.showToast({\n                        title: '选择图片失败',\n                        icon: 'none'\n                    });\n                }\n            });\n        },\n\n        // 处理选择的图片\n        handleImageSelected(imagePath) {\n            this.photos[this.currentPage] = imagePath;\n\n            const pageText = this.currentPage === 'master' ? '户主页' : '个人信息页';\n            uni.showToast({\n                title: `${pageText}拍摄完成`,\n                icon: 'success'\n            });\n\n            // 检查是否两页都已拍摄\n            if (this.canPrint) {\n                setTimeout(() => {\n                    this.proceedToPrintSettings();\n                }, 1500);\n            }\n        },\n        \n        // 重新拍照\n        resetPhotos() {\n            uni.showModal({\n                title: '确认重新拍照',\n                content: '是否要清除所有已拍摄的照片？',\n                success: (res) => {\n                    if (res.confirm) {\n                        this.photos = {\n                            master: '',\n                            personal: ''\n                        };\n                        uni.showToast({\n                            title: '已清除照片',\n                            icon: 'success'\n                        });\n                    }\n                }\n            });\n        },\n        \n        // 跳转到打印设置\n        proceedToPrintSettings() {\n            uni.showModal({\n                title: '拍摄完成',\n                content: '户口本两页已拍摄完成，是否进入打印设置？',\n                success: (res) => {\n                    if (res.confirm) {\n                        this.confirmPrint();\n                    }\n                }\n            });\n        },\n\n        // 确认打印\n        confirmPrint() {\n            if (!this.canPrint) {\n                uni.showToast({\n                    title: '请先拍摄户主页和个人信息页',\n                    icon: 'none'\n                });\n                return;\n            }\n\n            // 跳转到打印设置页面，传递照片数据\n            const photosData = encodeURIComponent(JSON.stringify(this.photos));\n            uni.navigateTo({\n\n            });\n        },\n\n        // 设置户口本模板图片 - 供后续引用\n        setHouseholdTemplateImages(masterImageUrl, personalImageUrl) {\n            this.masterPageImage = masterImageUrl;\n            this.personalPageImage = personalImageUrl;\n        }\n    },\n\n    // 页面加载时的处理\n    onLoad() {\n        // 这里可以设置默认的户口本模板图片\n        // 示例：this.setHouseholdTemplateImages('/static/images/household-master.png', '/static/images/household-personal.png');\n    }\n};\n</script>\n\n<style lang=\"scss\">\n.household-print {\n    background-color: #f5f5f5;\n    min-height: 100vh;\n    padding-bottom: 120rpx; // 为底部按钮留出空间\n}\n\n.tip-banner {\n    background: linear-gradient(135deg, #FFE082 0%, #FFA726 100%);\n    padding: 20rpx;\n    margin: 20rpx;\n    border-radius: 12rpx;\n    text-align: center;\n\n    .tip-text {\n        color: #8D6E63;\n        font-size: 26rpx;\n        font-weight: 500;\n    }\n}\n\n.page-card {\n    background-color: #fff;\n    margin: 20rpx;\n    border-radius: 16rpx;\n    overflow: hidden;\n    box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);\n\n    .page-header {\n        background-color: #f8f9fa;\n        padding: 30rpx;\n        border-bottom: 2rpx solid #e9ecef;\n\n        .page-title {\n            font-size: 32rpx;\n            font-weight: bold;\n            color: #333;\n        }\n    }\n\n    .page-content {\n        position: relative;\n        height: 500rpx; // 固定高度，模拟户口本页面比例\n        overflow: hidden;\n\n        &:active {\n            opacity: 0.9;\n        }\n    }\n}\n\n// 户口本页面图片样式\n.household-print {\n    &__page-image {\n        width: 100%;\n        height: 100%;\n        position: absolute;\n        top: 0;\n        left: 0;\n        object-fit: cover;\n    }\n\n    &__page-placeholder {\n        width: 100%;\n        height: 100%;\n        position: absolute;\n        top: 0;\n        left: 0;\n        background: linear-gradient(135deg, #F5F5F5 0%, #E8E8E8 100%);\n        display: flex;\n        flex-direction: column;\n        justify-content: center;\n        align-items: center;\n        border: 2rpx dashed #CCCCCC;\n        box-sizing: border-box;\n    }\n\n    &__placeholder-text {\n        font-size: 28rpx;\n        color: #999999;\n        font-weight: 500;\n        margin-bottom: 8rpx;\n    }\n\n    &__placeholder-desc {\n        font-size: 24rpx;\n        color: #BBBBBB;\n    }\n\n    // 相机覆盖层样式\n    &__camera-overlay {\n        position: absolute;\n        top: 0;\n        left: 0;\n        right: 0;\n        bottom: 0;\n        background: rgba(0, 0, 0, 0.4);\n        display: flex;\n        flex-direction: column;\n        justify-content: center;\n        align-items: center;\n        gap: 20rpx;\n    }\n\n    &__camera {\n        width: 120rpx;\n        height: 120rpx;\n        background-color: rgba(0, 0, 0, 0.6);\n        border-radius: 50%;\n        display: flex;\n        align-items: center;\n        justify-content: center;\n    }\n\n    &__text {\n        font-size: 32rpx;\n        color: #FFFFFF;\n        font-weight: 500;\n        text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.5);\n        text-align: center;\n    }\n}\n\n\n\n\n\n\n\n.action-buttons {\n    position: fixed;\n    bottom: 0;\n    left: 0;\n    right: 0;\n    background-color: #fff;\n    padding: 30rpx;\n    border-top: 1rpx solid #e9ecef;\n    display: flex;\n    gap: 20rpx;\n\n    .btn {\n        flex: 1;\n        height: 88rpx;\n        border-radius: 44rpx;\n        font-size: 32rpx;\n        font-weight: 500;\n        border: none;\n\n        &-secondary {\n            background-color: #f8f9fa;\n            color: #6c757d;\n        }\n\n        &-primary {\n            background-color: #FFA500;\n            color: #fff;\n\n            &:disabled {\n                background-color: #ccc;\n                color: #999;\n            }\n        }\n    }\n}\n\n/* 适配不同屏幕尺寸 */\n@media screen and (max-width: 375px) {\n    .page-card {\n        margin: 15rpx;\n\n        .page-content {\n            height: 450rpx;\n        }\n    }\n\n    .household-print {\n        &__camera {\n            width: 100rpx;\n            height: 100rpx;\n        }\n\n        &__text {\n            font-size: 28rpx;\n        }\n\n        &__placeholder-text {\n            font-size: 24rpx;\n        }\n\n        &__placeholder-desc {\n            font-size: 20rpx;\n        }\n    }\n\n    .action-buttons {\n        padding: 20rpx;\n\n        .btn {\n            height: 80rpx;\n            font-size: 28rpx;\n        }\n    }\n}\n</style>\n", "import MiniProgramPage from 'D:/Yunchuang/PhotoFront/fotoBox-front/pages/copy/household-print.vue'\nwx.createPage(MiniProgramPage)"], "names": ["uni"], "mappings": ";;AAsFA,MAAK,YAAU;AAAA,EACX,OAAO;AACH,WAAO;AAAA,MACH,aAAa;AAAA;AAAA,MACb,QAAQ;AAAA,QACJ,QAAQ;AAAA;AAAA,QACR,UAAU;AAAA;AAAA,MACb;AAAA;AAAA,MAED,iBAAiB;AAAA;AAAA,MACjB,mBAAmB;AAAA;AAAA;EAE1B;AAAA,EACD,UAAU;AAAA;AAAA,IAEN,WAAW;AACP,aAAO,KAAK,OAAO,UAAU,KAAK,OAAO;AAAA,IAC7C;AAAA,EACH;AAAA,EACD,SAAS;AAAA;AAAA,IAEL,cAAc;AACVA,oBAAG,MAAC,aAAY;AAAA,IACnB;AAAA;AAAA,IAGD,gBAAgB,MAAM;AAClB,WAAK,cAAc;AAGnBA,oBAAAA,MAAI,gBAAgB;AAAA,QAChB,UAAU,CAAC,MAAM,OAAO;AAAA,QACxB,SAAS,CAAC,QAAQ;AACd,cAAI,IAAI,aAAa,GAAG;AACpB,iBAAK,UAAS;AAAA,qBACP,IAAI,aAAa,GAAG;AAC3B,iBAAK,gBAAe;AAAA,UACxB;AAAA,QACJ;AAAA,MACJ,CAAC;AAAA,IACJ;AAAA;AAAA,IAGD,YAAY;AACRA,oBAAAA,MAAI,YAAY;AAAA,QACZ,OAAO;AAAA,QACP,YAAY,CAAC,QAAQ;AAAA,QACrB,SAAS,CAAC,QAAQ;AACd,eAAK,oBAAoB,IAAI,cAAc,CAAC,CAAC;AAAA,QAChD;AAAA,QACD,MAAM,CAAC,QAAQ;AACXA,wBAAA,MAAA,MAAA,SAAA,yCAAc,SAAS,GAAG;AAC1BA,wBAAAA,MAAI,UAAU;AAAA,YACV,OAAO;AAAA,YACP,MAAM;AAAA,UACV,CAAC;AAAA,QACL;AAAA,MACJ,CAAC;AAAA,IACJ;AAAA;AAAA,IAGD,kBAAkB;AACdA,oBAAAA,MAAI,YAAY;AAAA,QACZ,OAAO;AAAA,QACP,YAAY,CAAC,OAAO;AAAA,QACpB,SAAS,CAAC,QAAQ;AACd,eAAK,oBAAoB,IAAI,cAAc,CAAC,CAAC;AAAA,QAChD;AAAA,QACD,MAAM,CAAC,QAAQ;AACXA,sFAAc,WAAW,GAAG;AAC5BA,wBAAAA,MAAI,UAAU;AAAA,YACV,OAAO;AAAA,YACP,MAAM;AAAA,UACV,CAAC;AAAA,QACL;AAAA,MACJ,CAAC;AAAA,IACJ;AAAA;AAAA,IAGD,oBAAoB,WAAW;AAC3B,WAAK,OAAO,KAAK,WAAW,IAAI;AAEhC,YAAM,WAAW,KAAK,gBAAgB,WAAW,QAAQ;AACzDA,oBAAAA,MAAI,UAAU;AAAA,QACV,OAAO,GAAG,QAAQ;AAAA,QAClB,MAAM;AAAA,MACV,CAAC;AAGD,UAAI,KAAK,UAAU;AACf,mBAAW,MAAM;AACb,eAAK,uBAAsB;AAAA,QAC9B,GAAE,IAAI;AAAA,MACX;AAAA,IACH;AAAA;AAAA,IAGD,cAAc;AACVA,oBAAAA,MAAI,UAAU;AAAA,QACV,OAAO;AAAA,QACP,SAAS;AAAA,QACT,SAAS,CAAC,QAAQ;AACd,cAAI,IAAI,SAAS;AACb,iBAAK,SAAS;AAAA,cACV,QAAQ;AAAA,cACR,UAAU;AAAA;AAEdA,0BAAAA,MAAI,UAAU;AAAA,cACV,OAAO;AAAA,cACP,MAAM;AAAA,YACV,CAAC;AAAA,UACL;AAAA,QACJ;AAAA,MACJ,CAAC;AAAA,IACJ;AAAA;AAAA,IAGD,yBAAyB;AACrBA,oBAAAA,MAAI,UAAU;AAAA,QACV,OAAO;AAAA,QACP,SAAS;AAAA,QACT,SAAS,CAAC,QAAQ;AACd,cAAI,IAAI,SAAS;AACb,iBAAK,aAAY;AAAA,UACrB;AAAA,QACJ;AAAA,MACJ,CAAC;AAAA,IACJ;AAAA;AAAA,IAGD,eAAe;AACX,UAAI,CAAC,KAAK,UAAU;AAChBA,sBAAAA,MAAI,UAAU;AAAA,UACV,OAAO;AAAA,UACP,MAAM;AAAA,QACV,CAAC;AACD;AAAA,MACJ;AAGmB,yBAAmB,KAAK,UAAU,KAAK,MAAM,CAAC;AACjEA,oBAAAA,MAAI,WAAW,CAEf,CAAC;AAAA,IACJ;AAAA;AAAA,IAGD,2BAA2B,gBAAgB,kBAAkB;AACzD,WAAK,kBAAkB;AACvB,WAAK,oBAAoB;AAAA,IAC7B;AAAA,EACH;AAAA;AAAA,EAGD,SAAS;AAAA,EAGT;AACJ;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACnPA,GAAG,WAAW,eAAe;"}