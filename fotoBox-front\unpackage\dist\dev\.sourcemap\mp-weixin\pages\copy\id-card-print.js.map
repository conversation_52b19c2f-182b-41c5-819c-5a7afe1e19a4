{"version": 3, "file": "id-card-print.js", "sources": ["pages/copy/id-card-print.vue", "C:/Program Files/HBuilderX.4.66.2025051912/plugins/uniapp-cli-vite/uniPage:/cGFnZXMvY29weS9pZC1jYXJkLXByaW50LnZ1ZQ"], "sourcesContent": ["<template>\n    <f-page-layout \n        title=\"身份证/居住证拍照打印\" \n        fallback-path=\"/pages/copy/index\"\n        @back-click=\"onBackClick\"\n    >\n        <view class=\"id-card-print\">\n            <view class=\"id-card-print__header\">\n                <text class=\"id-card-print__tip\">内容拍照时需要与背景颜色尽量区分</text>\n            </view>\n            \n            <view class=\"id-card-print__content\">\n                <view class=\"id-card-print__title\">\n                    身份证/居住证拍照打印\n                </view>\n                \n                <!-- 正面拍摄区域 -->\n                <view class=\"id-card-print__preview\" @click=\"showActionSheet('front')\">\n                    <view class=\"id-card-print__card id-card-print__card--front\">\n                        <!-- 身份证正面背景图片 -->\n                        <image\n                            v-if=\"frontCardImage\"\n                            class=\"id-card-print__card-image\"\n                            :src=\"frontCardImage\"\n                            mode=\"aspectFill\"\n                        />\n                        <!-- 无图片时的占位符 -->\n                        <view v-else class=\"id-card-print__card-placeholder\">\n                            <view class=\"id-card-print__placeholder-text\">身份证正面模板</view>\n                            <view class=\"id-card-print__placeholder-desc\">图片将在此处显示</view>\n                        </view>\n\n                        <!-- 相机图标覆盖层 -->\n                        <view class=\"id-card-print__camera-overlay\">\n                            <view class=\"id-card-print__camera\">\n                                <uni-icons type=\"camera\" color=\"#FFFFFF\" size=\"30\"></uni-icons>\n                            </view>\n\n                            <!-- 拍摄文本 -->\n                            <view class=\"id-card-print__text\">\n                                拍摄正面\n                            </view>\n                        </view>\n                    </view>\n                </view>\n                \n                <!-- 反面拍摄区域 -->\n                <view class=\"id-card-print__preview\" @click=\"showActionSheet('back')\">\n                    <view class=\"id-card-print__card id-card-print__card--back\">\n                        <!-- 身份证背面背景图片 -->\n                        <image\n                            v-if=\"backCardImage\"\n                            class=\"id-card-print__card-image\"\n                            :src=\"backCardImage\"\n                            mode=\"aspectFill\"\n                        />\n                        <!-- 无图片时的占位符 -->\n                        <view v-else class=\"id-card-print__card-placeholder\">\n                            <view class=\"id-card-print__placeholder-text\">身份证背面模板</view>\n                            <view class=\"id-card-print__placeholder-desc\">图片将在此处显示</view>\n                        </view>\n\n                        <!-- 相机图标覆盖层 -->\n                        <view class=\"id-card-print__camera-overlay\">\n                            <view class=\"id-card-print__camera\">\n                                <uni-icons type=\"camera\" color=\"#FFFFFF\" size=\"30\"></uni-icons>\n                            </view>\n\n                            <!-- 拍摄文本 -->\n                            <view class=\"id-card-print__text\">\n                                拍摄背面\n                            </view>\n                        </view>\n                    </view>\n                </view>\n            </view>\n        </view>\n    </f-page-layout>\n</template>\n\n<script>\nexport default {\n    data() {\n        return {\n            currentSide: '', // 'front' 或 'back'\n            frontImage: '', // 正面拍摄图片\n            backImage: '', // 背面拍摄图片\n            // 身份证模板图片 - 留空供后续引用\n            frontCardImage: '', // 正面模板图片路径\n            backCardImage: '' // 背面模板图片路径\n        }\n    },\n    methods: {\n        // 返回上一页\n        onBackClick() {\n            uni.navigateBack({\n                delta: 1\n            });\n        },\n        \n        // 显示操作选择\n        showActionSheet(side) {\n            this.currentSide = side;\n            const sideText = side === 'front' ? '正面' : '反面';\n            \n            uni.showActionSheet({\n                itemList: ['拍照', '从相册选择'],\n                success: (res) => {\n                    if (res.tapIndex === 0) {\n                        this.takePhoto();\n                    } else if (res.tapIndex === 1) {\n                        this.chooseFromAlbum();\n                    }\n                }\n            });\n        },\n        \n        // 拍照\n        takePhoto() {\n            uni.chooseImage({\n                count: 1,\n                sourceType: ['camera'],\n                success: (res) => {\n                    console.log('拍照成功:', res.tempFilePaths[0]);\n                    this.previewPhoto(res.tempFilePaths[0]);\n                },\n                fail: () => {\n                    uni.showToast({\n                        title: '拍照取消',\n                        icon: 'none'\n                    });\n                }\n            });\n        },\n        \n        // 从相册选择\n        chooseFromAlbum() {\n            uni.chooseImage({\n                count: 1,\n                sourceType: ['album'],\n                success: (res) => {\n                    console.log('选择照片成功:', res.tempFilePaths[0]);\n                    this.previewPhoto(res.tempFilePaths[0]);\n                },\n                fail: () => {\n                    uni.showToast({\n                        title: '选择照片取消',\n                        icon: 'none'\n                    });\n                }\n            });\n        },\n        \n        // 处理选择的图片\n        previewPhoto(path) {\n            if (this.currentSide === 'front') {\n                this.frontImage = path;\n            } else {\n                this.backImage = path;\n            }\n\n            const sideText = this.currentSide === 'front' ? '正面' : '背面';\n\n            // 检查是否两面都已拍摄\n            if (this.frontImage && this.backImage) {\n                this.proceedToPrintSettings();\n            } else {\n                const nextSide = this.currentSide === 'front' ? '背面' : '正面';\n                uni.showToast({\n                    title: `请继续拍摄${nextSide}`,\n                    icon: 'none'\n                });\n            }\n        },\n\n        // 跳转到打印设置\n        proceedToPrintSettings() {\n            uni.showModal({\n                title: '拍摄完成',\n                content: '身份证正反面已拍摄完成，是否进入打印设置？',\n                success: (res) => {\n                    if (res.confirm) {\n                        uni.navigateTo({\n                            \n                        });\n                    }\n                }\n            });\n        },\n\n        // 设置身份证模板图片 - 供后续引用\n        setIdCardTemplateImages(frontImageUrl, backImageUrl) {\n            this.frontCardImage = frontImageUrl;\n            this.backCardImage = backImageUrl;\n        }\n    },\n\n    // 页面加载时的处理\n    onLoad() {\n        // 这里可以设置默认的身份证模板图片\n        // 示例：this.setIdCardTemplateImages('/static/images/id-card-front.png', '/static/images/id-card-back.png');\n    }\n}\n</script>\n\n<style lang=\"scss\">\n.id-card-print {\n    background-color: #FFF8E1;\n    min-height: 100vh;\n    display: flex;\n    flex-direction: column;\n    \n    &__header {\n        background-color: #FFD180;\n        padding: 16rpx;\n        text-align: center;\n    }\n    \n    &__tip {\n        font-size: 26rpx;\n        color: #795548;\n    }\n    \n    &__content {\n        flex: 1;\n        display: flex;\n        flex-direction: column;\n        align-items: center;\n        padding: 30rpx 20rpx;\n    }\n    \n    &__title {\n        font-size: 46rpx;\n        font-weight: bold;\n        color: #FFA000;\n        margin-bottom: 50rpx;\n        text-align: center;\n    }\n    \n    &__preview {\n        width: 100%;\n        display: flex;\n        justify-content: center;\n        padding: 0 30rpx;\n        margin-bottom: 40rpx;\n        \n        &:last-child {\n            margin-bottom: 0;\n        }\n    }\n    \n    &__card {\n        width: 100%;\n        max-width: 550rpx;\n        aspect-ratio: 1.586; // 身份证比例 85.6mm × 54mm\n        background-color: #fff;\n        border-radius: 12rpx;\n        box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.08);\n        display: flex;\n        flex-direction: column;\n        position: relative;\n        overflow: hidden;\n    }\n\n    &__card-image {\n        width: 100%;\n        height: 100%;\n        position: absolute;\n        top: 0;\n        left: 0;\n        border-radius: 12rpx;\n        object-fit: cover;\n    }\n\n    &__card-placeholder {\n        width: 100%;\n        height: 100%;\n        position: absolute;\n        top: 0;\n        left: 0;\n        border-radius: 12rpx;\n        background: linear-gradient(135deg, #F5F5F5 0%, #E8E8E8 100%);\n        display: flex;\n        flex-direction: column;\n        justify-content: center;\n        align-items: center;\n        border: 2rpx dashed #CCCCCC;\n        box-sizing: border-box;\n    }\n\n    &__placeholder-text {\n        font-size: 28rpx;\n        color: #999999;\n        font-weight: 500;\n        margin-bottom: 8rpx;\n    }\n\n    &__placeholder-desc {\n        font-size: 24rpx;\n        color: #BBBBBB;\n    }\n    \n    // 相机覆盖层样式\n    &__camera-overlay {\n        position: absolute;\n        top: 0;\n        left: 0;\n        right: 0;\n        bottom: 0;\n        background: rgba(0, 0, 0, 0.4);\n        display: flex;\n        flex-direction: column;\n        justify-content: center;\n        align-items: center;\n        gap: 20rpx;\n        border-radius: 12rpx;\n    }\n    \n    &__camera {\n        width: 120rpx;\n        height: 120rpx;\n        background-color: rgba(0, 0, 0, 0.6);\n        border-radius: 50%;\n        display: flex;\n        align-items: center;\n        justify-content: center;\n    }\n\n    &__text {\n        font-size: 32rpx;\n        color: #FFFFFF;\n        font-weight: 500;\n        text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.5);\n        text-align: center;\n    }\n}\n\n/* 适配不同屏幕尺寸 */\n@media screen and (max-width: 375px) {\n    .id-card-print {\n        &__card {\n            max-width: 500rpx;\n        }\n\n        &__camera {\n            width: 100rpx;\n            height: 100rpx;\n        }\n\n        &__text {\n            font-size: 28rpx;\n        }\n\n        &__placeholder-text {\n            font-size: 24rpx;\n        }\n\n        &__placeholder-desc {\n            font-size: 20rpx;\n        }\n    }\n}\n</style>", "import MiniProgramPage from 'D:/Yunchuang/PhotoFront/fotoBox-front/pages/copy/id-card-print.vue'\nwx.createPage(MiniProgramPage)"], "names": ["uni"], "mappings": ";;AAiFA,MAAK,YAAU;AAAA,EACX,OAAO;AACH,WAAO;AAAA,MACH,aAAa;AAAA;AAAA,MACb,YAAY;AAAA;AAAA,MACZ,WAAW;AAAA;AAAA;AAAA,MAEX,gBAAgB;AAAA;AAAA,MAChB,eAAe;AAAA;AAAA,IACnB;AAAA,EACH;AAAA,EACD,SAAS;AAAA;AAAA,IAEL,cAAc;AACVA,oBAAAA,MAAI,aAAa;AAAA,QACb,OAAO;AAAA,MACX,CAAC;AAAA,IACJ;AAAA;AAAA,IAGD,gBAAgB,MAAM;AAClB,WAAK,cAAc;AAGnBA,oBAAAA,MAAI,gBAAgB;AAAA,QAChB,UAAU,CAAC,MAAM,OAAO;AAAA,QACxB,SAAS,CAAC,QAAQ;AACd,cAAI,IAAI,aAAa,GAAG;AACpB,iBAAK,UAAS;AAAA,qBACP,IAAI,aAAa,GAAG;AAC3B,iBAAK,gBAAe;AAAA,UACxB;AAAA,QACJ;AAAA,MACJ,CAAC;AAAA,IACJ;AAAA;AAAA,IAGD,YAAY;AACRA,oBAAAA,MAAI,YAAY;AAAA,QACZ,OAAO;AAAA,QACP,YAAY,CAAC,QAAQ;AAAA,QACrB,SAAS,CAAC,QAAQ;AACdA,8BAAY,MAAA,OAAA,uCAAA,SAAS,IAAI,cAAc,CAAC,CAAC;AACzC,eAAK,aAAa,IAAI,cAAc,CAAC,CAAC;AAAA,QACzC;AAAA,QACD,MAAM,MAAM;AACRA,wBAAAA,MAAI,UAAU;AAAA,YACV,OAAO;AAAA,YACP,MAAM;AAAA,UACV,CAAC;AAAA,QACL;AAAA,MACJ,CAAC;AAAA,IACJ;AAAA;AAAA,IAGD,kBAAkB;AACdA,oBAAAA,MAAI,YAAY;AAAA,QACZ,OAAO;AAAA,QACP,YAAY,CAAC,OAAO;AAAA,QACpB,SAAS,CAAC,QAAQ;AACdA,kFAAY,WAAW,IAAI,cAAc,CAAC,CAAC;AAC3C,eAAK,aAAa,IAAI,cAAc,CAAC,CAAC;AAAA,QACzC;AAAA,QACD,MAAM,MAAM;AACRA,wBAAAA,MAAI,UAAU;AAAA,YACV,OAAO;AAAA,YACP,MAAM;AAAA,UACV,CAAC;AAAA,QACL;AAAA,MACJ,CAAC;AAAA,IACJ;AAAA;AAAA,IAGD,aAAa,MAAM;AACf,UAAI,KAAK,gBAAgB,SAAS;AAC9B,aAAK,aAAa;AAAA,aACf;AACH,aAAK,YAAY;AAAA,MACrB;AAEiB,WAAK,gBAAgB,UAAU,OAAO;AAGvD,UAAI,KAAK,cAAc,KAAK,WAAW;AACnC,aAAK,uBAAsB;AAAA,aACxB;AACH,cAAM,WAAW,KAAK,gBAAgB,UAAU,OAAO;AACvDA,sBAAAA,MAAI,UAAU;AAAA,UACV,OAAO,QAAQ,QAAQ;AAAA,UACvB,MAAM;AAAA,QACV,CAAC;AAAA,MACL;AAAA,IACH;AAAA;AAAA,IAGD,yBAAyB;AACrBA,oBAAAA,MAAI,UAAU;AAAA,QACV,OAAO;AAAA,QACP,SAAS;AAAA,QACT,SAAS,CAAC,QAAQ;AACd,cAAI,IAAI,SAAS;AACbA,0BAAAA,MAAI,WAAW,CAEf,CAAC;AAAA,UACL;AAAA,QACJ;AAAA,MACJ,CAAC;AAAA,IACJ;AAAA;AAAA,IAGD,wBAAwB,eAAe,cAAc;AACjD,WAAK,iBAAiB;AACtB,WAAK,gBAAgB;AAAA,IACzB;AAAA,EACH;AAAA;AAAA,EAGD,SAAS;AAAA,EAGT;AACJ;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACzMA,GAAG,WAAW,eAAe;"}