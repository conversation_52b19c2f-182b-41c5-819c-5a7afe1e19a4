"use strict";
const common_vendor = require("../../common/vendor.js");
const _sfc_main = {
  methods: {
    // 返回上一页
    onBackClick() {
      common_vendor.index.navigateBack({
        delta: 1
      });
    },
    // 处理选择
    handleSelect(type) {
      common_vendor.index.__f__("log", "at pages/photo/index.vue:174", "选择照片类型:", type);
      switch (type) {
        case "portrait":
          common_vendor.index.navigateTo({
            url: "/pages/photo/six-inch-photo"
          });
          break;
        case "package":
          common_vendor.index.showToast({
            title: "选择了照片套餐",
            icon: "none"
          });
          break;
        case "magic":
          common_vendor.index.showToast({
            title: "选择了魔镜魔镜",
            icon: "none"
          });
          break;
        case "beauty":
          common_vendor.index.showToast({
            title: "选择了人像美颜",
            icon: "none"
          });
          break;
        case "repair":
          common_vendor.index.showToast({
            title: "选择了老照片修复",
            icon: "none"
          });
          break;
        case "collage":
          common_vendor.index.showToast({
            title: "选择了拼图",
            icon: "none"
          });
          break;
        case "polaroid":
          common_vendor.index.showToast({
            title: "选择了拍立得风格",
            icon: "none"
          });
          break;
        case "sticker":
          common_vendor.index.showToast({
            title: "选择了大头贴",
            icon: "none"
          });
          break;
        case "inch":
          common_vendor.index.navigateTo({
            url: "/pages/id-photo/index"
          });
          break;
        case "small":
          common_vendor.index.navigateTo({
            url: "/pages/photo/five-inch-photo"
          });
          break;
        case "a4":
          common_vendor.index.navigateTo({
            url: "/pages/photo/a4-photo"
          });
          break;
        case "calendar":
          common_vendor.index.showToast({
            title: "选择了台历相册",
            icon: "none"
          });
          break;
      }
    }
  }
};
if (!Array) {
  const _easycom_uni_icons2 = common_vendor.resolveComponent("uni-icons");
  const _component_f_page_layout = common_vendor.resolveComponent("f-page-layout");
  (_easycom_uni_icons2 + _component_f_page_layout)();
}
const _easycom_uni_icons = () => "../../uni_modules/uni-icons/components/uni-icons/uni-icons.js";
if (!Math) {
  _easycom_uni_icons();
}
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return {
    a: common_vendor.p({
      type: "image",
      color: "#409EFF",
      size: "24"
    }),
    b: common_vendor.o(($event) => $options.handleSelect("portrait")),
    c: common_vendor.p({
      type: "gift",
      color: "#FF8C00",
      size: "24"
    }),
    d: common_vendor.o(($event) => $options.handleSelect("package")),
    e: common_vendor.p({
      type: "image",
      color: "#7D7DFF",
      size: "24"
    }),
    f: common_vendor.o(($event) => $options.handleSelect("magic")),
    g: common_vendor.p({
      type: "camera",
      color: "#7D7DFF",
      size: "20"
    }),
    h: common_vendor.o(($event) => $options.handleSelect("beauty")),
    i: common_vendor.p({
      type: "refresh",
      color: "#7DFF7D",
      size: "20"
    }),
    j: common_vendor.o(($event) => $options.handleSelect("repair")),
    k: common_vendor.p({
      type: "images",
      color: "#FF7D7D",
      size: "24"
    }),
    l: common_vendor.o(($event) => $options.handleSelect("collage")),
    m: common_vendor.p({
      type: "camera",
      color: "#FF7D7D",
      size: "20"
    }),
    n: common_vendor.o(($event) => $options.handleSelect("polaroid")),
    o: common_vendor.p({
      type: "paperclip",
      color: "#409EFF",
      size: "20"
    }),
    p: common_vendor.o(($event) => $options.handleSelect("sticker")),
    q: common_vendor.p({
      type: "person",
      color: "#FF8C00",
      size: "20"
    }),
    r: common_vendor.o(($event) => $options.handleSelect("inch")),
    s: common_vendor.p({
      type: "image",
      color: "#7D7DFF",
      size: "20"
    }),
    t: common_vendor.o(($event) => $options.handleSelect("small")),
    v: common_vendor.p({
      type: "paperclip",
      color: "#FF7D7D",
      size: "20"
    }),
    w: common_vendor.o(($event) => $options.handleSelect("a4")),
    x: common_vendor.p({
      type: "calendar",
      color: "#7DFF7D",
      size: "20"
    }),
    y: common_vendor.o(($event) => $options.handleSelect("calendar")),
    z: common_vendor.o($options.onBackClick),
    A: common_vendor.p({
      title: "照片打印",
      ["fallback-path"]: "/pages/index/index"
    })
  };
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages/photo/index.js.map
