<template>
    <f-page-layout 
        title="身份证/居住证拍照打印" 
        fallback-path="/pages/copy/index"
        @back-click="onBackClick"
    >
        <view class="id-card-print">
            <view class="id-card-print__header">
                <text class="id-card-print__tip">内容拍照时需要与背景颜色尽量区分</text>
            </view>
            
            <view class="id-card-print__content">
                <view class="id-card-print__title">
                    身份证/居住证拍照打印
                </view>
                
                <!-- 正面拍摄区域 -->
                <view class="id-card-print__preview" @click="showActionSheet('front')">
                    <view class="id-card-print__card id-card-print__card--front">
                        <!-- 身份证正面背景图片 -->
                        <image
                            v-if="frontCardImage"
                            class="id-card-print__card-image"
                            :src="frontCardImage"
                            mode="aspectFill"
                        />
                        <!-- 无图片时的占位符 -->
                        <view v-else class="id-card-print__card-placeholder">
                            <view class="id-card-print__placeholder-text">身份证正面模板</view>
                            <view class="id-card-print__placeholder-desc">图片将在此处显示</view>
                        </view>

                        <!-- 相机图标覆盖层 -->
                        <view class="id-card-print__camera-overlay">
                            <view class="id-card-print__camera">
                                <uni-icons type="camera" color="#FFFFFF" size="30"></uni-icons>
                            </view>

                            <!-- 拍摄文本 -->
                            <view class="id-card-print__text">
                                拍摄正面
                            </view>
                        </view>
                    </view>
                </view>
                
                <!-- 反面拍摄区域 -->
                <view class="id-card-print__preview" @click="showActionSheet('back')">
                    <view class="id-card-print__card id-card-print__card--back">
                        <!-- 身份证背面背景图片 -->
                        <image
                            v-if="backCardImage"
                            class="id-card-print__card-image"
                            :src="backCardImage"
                            mode="aspectFill"
                        />
                        <!-- 无图片时的占位符 -->
                        <view v-else class="id-card-print__card-placeholder">
                            <view class="id-card-print__placeholder-text">身份证背面模板</view>
                            <view class="id-card-print__placeholder-desc">图片将在此处显示</view>
                        </view>

                        <!-- 相机图标覆盖层 -->
                        <view class="id-card-print__camera-overlay">
                            <view class="id-card-print__camera">
                                <uni-icons type="camera" color="#FFFFFF" size="30"></uni-icons>
                            </view>

                            <!-- 拍摄文本 -->
                            <view class="id-card-print__text">
                                拍摄背面
                            </view>
                        </view>
                    </view>
                </view>
            </view>
        </view>
    </f-page-layout>
</template>

<script>
export default {
    data() {
        return {
            currentSide: '', // 'front' 或 'back'
            frontImage: '', // 正面拍摄图片
            backImage: '', // 背面拍摄图片
            // 身份证模板图片 - 留空供后续引用
            frontCardImage: '', // 正面模板图片路径
            backCardImage: '' // 背面模板图片路径
        }
    },
    methods: {
        // 返回上一页
        onBackClick() {
            uni.navigateBack({
                delta: 1
            });
        },
        
        // 显示操作选择
        showActionSheet(side) {
            this.currentSide = side;
            const sideText = side === 'front' ? '正面' : '反面';
            
            uni.showActionSheet({
                itemList: ['拍照', '从相册选择'],
                success: (res) => {
                    if (res.tapIndex === 0) {
                        this.takePhoto();
                    } else if (res.tapIndex === 1) {
                        this.chooseFromAlbum();
                    }
                }
            });
        },
        
        // 拍照
        takePhoto() {
            uni.chooseImage({
                count: 1,
                sourceType: ['camera'],
                success: (res) => {
                    console.log('拍照成功:', res.tempFilePaths[0]);
                    this.previewPhoto(res.tempFilePaths[0]);
                },
                fail: () => {
                    uni.showToast({
                        title: '拍照取消',
                        icon: 'none'
                    });
                }
            });
        },
        
        // 从相册选择
        chooseFromAlbum() {
            uni.chooseImage({
                count: 1,
                sourceType: ['album'],
                success: (res) => {
                    console.log('选择照片成功:', res.tempFilePaths[0]);
                    this.previewPhoto(res.tempFilePaths[0]);
                },
                fail: () => {
                    uni.showToast({
                        title: '选择照片取消',
                        icon: 'none'
                    });
                }
            });
        },
        
        // 处理选择的图片
        previewPhoto(path) {
            if (this.currentSide === 'front') {
                this.frontImage = path;
            } else {
                this.backImage = path;
            }

            const sideText = this.currentSide === 'front' ? '正面' : '背面';

            // 检查是否两面都已拍摄
            if (this.frontImage && this.backImage) {
                this.proceedToPrintSettings();
            } else {
                const nextSide = this.currentSide === 'front' ? '背面' : '正面';
                uni.showToast({
                    title: `请继续拍摄${nextSide}`,
                    icon: 'none'
                });
            }
        },

        // 跳转到打印设置
        proceedToPrintSettings() {
            uni.showModal({
                title: '拍摄完成',
                content: '身份证正反面已拍摄完成，是否进入打印设置？',
                success: (res) => {
                    if (res.confirm) {
                        uni.navigateTo({
                            
                        });
                    }
                }
            });
        },

        // 设置身份证模板图片 - 供后续引用
        setIdCardTemplateImages(frontImageUrl, backImageUrl) {
            this.frontCardImage = frontImageUrl;
            this.backCardImage = backImageUrl;
        }
    },

    // 页面加载时的处理
    onLoad() {
        // 这里可以设置默认的身份证模板图片
        // 示例：this.setIdCardTemplateImages('/static/images/id-card-front.png', '/static/images/id-card-back.png');
    }
}
</script>

<style lang="scss">
.id-card-print {
    background-color: #FFF8E1;
    min-height: 100vh;
    display: flex;
    flex-direction: column;
    
    &__header {
        background-color: #FFD180;
        padding: 16rpx;
        text-align: center;
    }
    
    &__tip {
        font-size: 26rpx;
        color: #795548;
    }
    
    &__content {
        flex: 1;
        display: flex;
        flex-direction: column;
        align-items: center;
        padding: 30rpx 20rpx;
    }
    
    &__title {
        font-size: 46rpx;
        font-weight: bold;
        color: #FFA000;
        margin-bottom: 50rpx;
        text-align: center;
    }
    
    &__preview {
        width: 100%;
        display: flex;
        justify-content: center;
        padding: 0 30rpx;
        margin-bottom: 40rpx;
        
        &:last-child {
            margin-bottom: 0;
        }
    }
    
    &__card {
        width: 100%;
        max-width: 550rpx;
        aspect-ratio: 1.586; // 身份证比例 85.6mm × 54mm
        background-color: #fff;
        border-radius: 12rpx;
        box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.08);
        display: flex;
        flex-direction: column;
        position: relative;
        overflow: hidden;
    }

    &__card-image {
        width: 100%;
        height: 100%;
        position: absolute;
        top: 0;
        left: 0;
        border-radius: 12rpx;
        object-fit: cover;
    }

    &__card-placeholder {
        width: 100%;
        height: 100%;
        position: absolute;
        top: 0;
        left: 0;
        border-radius: 12rpx;
        background: linear-gradient(135deg, #F5F5F5 0%, #E8E8E8 100%);
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        border: 2rpx dashed #CCCCCC;
        box-sizing: border-box;
    }

    &__placeholder-text {
        font-size: 28rpx;
        color: #999999;
        font-weight: 500;
        margin-bottom: 8rpx;
    }

    &__placeholder-desc {
        font-size: 24rpx;
        color: #BBBBBB;
    }
    
    // 相机覆盖层样式
    &__camera-overlay {
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: rgba(0, 0, 0, 0.4);
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        gap: 20rpx;
        border-radius: 12rpx;
    }
    
    &__camera {
        width: 120rpx;
        height: 120rpx;
        background-color: rgba(0, 0, 0, 0.6);
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
    }

    &__text {
        font-size: 32rpx;
        color: #FFFFFF;
        font-weight: 500;
        text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.5);
        text-align: center;
    }
}

/* 适配不同屏幕尺寸 */
@media screen and (max-width: 375px) {
    .id-card-print {
        &__card {
            max-width: 500rpx;
        }

        &__camera {
            width: 100rpx;
            height: 100rpx;
        }

        &__text {
            font-size: 28rpx;
        }

        &__placeholder-text {
            font-size: 24rpx;
        }

        &__placeholder-desc {
            font-size: 20rpx;
        }
    }
}
</style>