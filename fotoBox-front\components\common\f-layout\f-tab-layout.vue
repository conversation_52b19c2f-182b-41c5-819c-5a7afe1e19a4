<template>
	<view class="f-tab-layout">
		<!-- 头部 -->
		<f-header 
			:location="location" 
			:bg-color="headerBgColor"
			@location-click="onLocationClick" 
		/>
		
		<!-- 内容区域 -->
		<view class="f-tab-layout__content">
			<slot></slot>
		</view>
		
		<!-- 底部标签栏 - 删除条件编译注释，使其在所有环境下都显示 -->
		<f-tabbar 
			:active-index="activeTabIndex" 
			@tab-click="onTabClick"
		/>
	</view>
</template>

<script>
	import FHeader from '@/components/common/f-navbar/f-header.vue';
	import FTabbar from '@/components/common/f-navbar/f-tabbar.vue';
	
	export default {
		name: 'FTabLayout',
		components: {
			FHeader,
			FTabbar
		},
		props: {
			// 位置信息
			location: {
				type: String,
				default: '[000000]未选取件地点！'
			},
			// 头部背景色
			headerBgColor: {
				type: String,
				default: '#FFA500' // 橙色背景
			},
			// 当前选中的标签页索引
			activeTabIndex: {
				type: Number,
				default: 0
			}
		},
		methods: {
			// 位置点击
			onLocationClick() {
				this.$emit('location-click');
			},
			// 标签页点击
			onTabClick(data) {
				this.$emit('tab-click', data);
			}
		}
	}
</script>

<style lang="scss">
	.f-tab-layout {
		min-height: 100vh;
		display: flex;
		flex-direction: column;
		background-color: #f5f5f5;
		
		&__content {
			flex: 1;
			padding-bottom: 100rpx; /* 在所有环境下添加底部内边距，为自定义tabBar留出空间 */
		}
	}
</style> 