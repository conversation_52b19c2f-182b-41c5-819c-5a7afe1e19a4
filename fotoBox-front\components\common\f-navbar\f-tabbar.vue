<template>
	<view class="f-tabbar">
		<view 
			v-for="(item, index) in tabbarItems" 
			:key="index"
			class="f-tabbar__item"
			:class="{'f-tabbar__item--active': activeIndex === index}"
			@click="onTabClick(index)"
		>
			<uni-icons :type="getIconType(item.icon, activeIndex === index)" :color="activeIndex === index ? '#FFA500' : '#999'" size="24"></uni-icons>
			<text>{{ item.text }}</text>
		</view>
	</view>
</template>

<script>
	export default {
		name: 'FTabbar',
		props: {
			// 当前选中的标签页索引
			activeIndex: {
				type: Number,
				default: 0
			},
			// 自定义标签项
			customItems: {
				type: Array,
				default: null
			}
		},
		data() {
			return {
				defaultItems: [
					{
						text: '首页',
						icon: 'home',
						path: '/pages/index/index'
					},
					{
						text: '网点',
						icon: 'location',
						path: '/pages/location/index'
					},
					{
						text: '客服',
						icon: 'chat',
						path: '/pages/service/index'
					},
					{
						text: '我的',
						icon: 'person',
						path: '/pages/user/index'
					}
				]
			};
		},
		computed: {
			tabbarItems() {
				return this.customItems || this.defaultItems;
			}
		},
		methods: {
			// 获取图标类型
			getIconType(icon, isActive) {
				// 如果是激活状态，返回实心图标
				const iconMap = {
					'home': isActive ? 'home-filled' : 'home',
					'location': isActive ? 'location-filled' : 'location',
					'chat': isActive ? 'chat-filled' : 'chat',
					'person': isActive ? 'person-filled' : 'person'
				};
				
				return iconMap[icon] || icon;
			},
			
			// 标签点击
			onTabClick(index) {
				if (index === this.activeIndex) return;
				
				const item = this.tabbarItems[index];
				// 传递给父组件
				this.$emit('tab-click', {
					index,
					item
				});
				
				// 直接处理导航逻辑，避免依赖原生 tabBar
				if (item && item.path) {
					// 检查是否有这个页面
					const mainPages = ['/pages/index/index', '/pages/location/index', '/pages/service/index', '/pages/user/index'];
					if (mainPages.includes(item.path)) {
						// 对于主要页面，直接使用 reLaunch 方法，它会关闭所有页面，打开到应用内的某个页面
						uni.reLaunch({
							url: item.path
						});
					} else {
						// 对于其他页面，使用 navigateTo
						uni.navigateTo({
							url: item.path
						});
					}
				}
			}
		}
	}
</script>

<style lang="scss">
	.f-tabbar {
		height: 100rpx;
		background-color: #fff;
		display: flex;
		justify-content: space-around;
		align-items: center;
		box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.05);
		position: fixed;
		left: 0;
		right: 0;
		bottom: 0;
		width: 100%;
		z-index: 999;
		
		&__item {
			display: flex;
			flex-direction: column;
			align-items: center;
			justify-content: center;
			height: 100%;
			flex: 1;
			
			text {
				font-size: 22rpx;
				color: #999;
				margin-top: 6rpx;
			}
			
			&--active {
				text {
					color: #FFA500;
				}
			}
		}
	}
	
	/* 适配不同屏幕尺寸 */
	@media screen and (max-width: 375px) {
		.f-tabbar {
			height: 90rpx;
			
			&__item {
				text {
					font-size: 20rpx;
				}
			}
		}
	}
	
	/* 适配大屏幕 */
	@media screen and (min-width: 768px) {
		.f-tabbar {
			height: 120rpx;
			
			&__item {
				text {
					font-size: 24rpx;
				}
			}
		}
	}
	
	/* 为了安全起见，添加适配底部安全区域的样式 */
	@supports (padding-bottom: env(safe-area-inset-bottom)) {
		.f-tabbar {
			padding-bottom: env(safe-area-inset-bottom);
		}
	}
</style> 