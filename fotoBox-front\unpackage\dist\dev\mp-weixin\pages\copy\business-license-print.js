"use strict";
const common_vendor = require("../../common/vendor.js");
const _sfc_main = {
  data() {
    return {
      businessLicensePhoto: "",
      // 用户拍摄的营业执照照片
      // 营业执照模板图片 - 留空供后续引用
      businessLicenseTemplateImage: "",
      // 营业执照模板图片路径
      // 表格行数据（模拟营业执照表格结构）
      tableRows: [
        ["", "", "", ""],
        ["", "", "", ""],
        ["", "", "", ""],
        ["", "", "", ""],
        ["", "", "", ""],
        ["", "", "", ""],
        ["", "", "", ""],
        ["", "", "", ""],
        ["", "", "", ""],
        ["", "", "", ""],
        ["", "", "", ""],
        ["", "", "", ""]
      ]
    };
  },
  methods: {
    // 返回上一页
    onBackClick() {
      common_vendor.index.navigateBack();
    },
    // 显示操作选择
    showActionSheet() {
      common_vendor.index.showActionSheet({
        itemList: ["拍照", "从相册选择"],
        success: (res) => {
          if (res.tapIndex === 0) {
            this.takePhoto();
          } else if (res.tapIndex === 1) {
            this.chooseFromAlbum();
          }
        }
      });
    },
    // 拍照功能
    takePhoto() {
      common_vendor.index.chooseImage({
        count: 1,
        sourceType: ["camera"],
        success: (res) => {
          this.handleImageSelected(res.tempFilePaths[0]);
        },
        fail: (err) => {
          common_vendor.index.__f__("error", "at pages/copy/business-license-print.vue:94", "拍照失败:", err);
          common_vendor.index.showToast({
            title: "拍照失败",
            icon: "none"
          });
        }
      });
    },
    // 从相册选择
    chooseFromAlbum() {
      common_vendor.index.chooseImage({
        count: 1,
        sourceType: ["album"],
        success: (res) => {
          this.handleImageSelected(res.tempFilePaths[0]);
        },
        fail: (err) => {
          common_vendor.index.__f__("error", "at pages/copy/business-license-print.vue:112", "选择图片失败:", err);
          common_vendor.index.showToast({
            title: "选择图片失败",
            icon: "none"
          });
        }
      });
    },
    // 处理选择的图片
    handleImageSelected(imagePath) {
      this.businessLicensePhoto = imagePath;
      common_vendor.index.showToast({
        title: "营业执照拍摄完成",
        icon: "success"
      });
      setTimeout(() => {
        this.proceedToPrintSettings();
      }, 1500);
    },
    // 跳转到打印设置
    proceedToPrintSettings() {
      common_vendor.index.showModal({
        title: "拍摄完成",
        content: "营业执照已拍摄完成，是否进入打印设置？",
        success: (res) => {
          if (res.confirm) {
            this.confirmPrint();
          }
        }
      });
    },
    // 确认打印
    confirmPrint() {
      if (!this.businessLicensePhoto) {
        common_vendor.index.showToast({
          title: "请先拍摄营业执照",
          icon: "none"
        });
        return;
      }
      encodeURIComponent(JSON.stringify({
        businessLicense: this.businessLicensePhoto
      }));
      common_vendor.index.navigateTo({});
    },
    // 设置营业执照模板图片 - 供后续引用
    setBusinessLicenseTemplateImage(templateImageUrl) {
      this.businessLicenseTemplateImage = templateImageUrl;
    }
  },
  // 页面加载时的处理
  onLoad() {
  }
};
if (!Array) {
  const _easycom_uni_icons2 = common_vendor.resolveComponent("uni-icons");
  const _component_f_page_layout = common_vendor.resolveComponent("f-page-layout");
  (_easycom_uni_icons2 + _component_f_page_layout)();
}
const _easycom_uni_icons = () => "../../uni_modules/uni-icons/components/uni-icons/uni-icons.js";
if (!Math) {
  _easycom_uni_icons();
}
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return {
    a: common_vendor.f($data.tableRows, (row, index, i0) => {
      return {
        a: common_vendor.f(row, (cell, cellIndex, i1) => {
          return {
            a: common_vendor.t(cell),
            b: cellIndex
          };
        }),
        b: index
      };
    }),
    b: common_vendor.p({
      type: "camera",
      color: "#FFFFFF",
      size: "40"
    }),
    c: common_vendor.o((...args) => $options.showActionSheet && $options.showActionSheet(...args)),
    d: common_vendor.o($options.onBackClick),
    e: common_vendor.p({
      title: "营业执照拍照打印",
      ["fallback-path"]: "/pages/copy/index"
    })
  };
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages/copy/business-license-print.js.map
