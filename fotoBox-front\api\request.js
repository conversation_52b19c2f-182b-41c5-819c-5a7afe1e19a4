/**
 * 请求拦截器和响应拦截器
 */

// 请求配置
import { baseURL } from '@/config/env';

// 请求拦截器
const request = (options) => {
  return new Promise((resolve, reject) => {
    // 请求前获取最新token
    const token = uni.getStorageSync('token');
    
    // 构建请求头
    const headers = {
      'Content-Type': 'application/json',
      ...options.header
    };
    
    // 如果有token且需要token，则添加Authorization头
    if (token) {
      headers['Authorization'] = token;
    }

    // 处理表单数据
    let requestData = options.data || {};
    if (headers['Content-Type'] === 'application/x-www-form-urlencoded') {
      // 将对象转换为表单字符串
      const formData = Object.keys(requestData)
          .map(key => `${encodeURIComponent(key)}=${encodeURIComponent(requestData[key] || '')}`)
          .join('&');
      requestData = formData;
    }
    
    uni.request({
      url: (options.baseURL || baseURL) + options.url,
      method: options.method || 'GET',
      data: options.data || {},
      header: headers,
      success: (res) => {
        // 处理登录状态失效
        if (res.data.code === 401) {
          // 清除本地token
          uni.removeStorageSync('token');
          uni.removeStorageSync('userInfo');
          
          uni.showToast({
            title: '登录已过期，请重新登录',
            icon: 'none'
          });
          
          // 跳转登录页
          setTimeout(() => {
            uni.navigateTo({
              url: '/pages/login/index'
            });
          }, 1500);
          
          reject(res.data);
          return;
        }
        
        resolve(res.data);
      },
      fail: (err) => {
        console.error('请求失败:', err);
        uni.showToast({
          title: '网络请求失败',
          icon: 'none'
        });
        reject(err);
      }
    });
  });
};

// 导出请求方法
export default request;
