<template>
	<view class="home-service-grid">
		<view class="home-service-grid__row">
			<view class="home-service-grid__item">
				<f-service-card 
					title="文档" 
					icon="compose" 
					icon-type="uni" 
					icon-color="#6698FF"
					@click="onServiceClick(serviceItems[0])"
				/>
			</view>
			<view class="home-service-grid__item">
				<f-service-card 
					title="照片" 
					icon="camera" 
					icon-type="uni" 
					icon-color="#FF6B98"
					@click="onServiceClick(serviceItems[1])"
				/>
			</view>
		</view>
		<view class="home-service-grid__row">
			<view class="home-service-grid__item">
				<f-service-card 
					title="证件照" 
					icon="person" 
					icon-type="uni" 
					icon-color="#FFB266"
					@click="onServiceClick(serviceItems[2])"
				/>
			</view>
			<view class="home-service-grid__item">
				<f-service-card 
					title="复印" 
					icon="paperclip" 
					icon-type="uni" 
					icon-color="#66FFB2"
					@click="onServiceClick(serviceItems[3])"
				/>
			</view>
		</view>
	</view>
</template>

<script>
	import FServiceCard from '@/components/common/f-service-card/f-service-card.vue';
	
	export default {
		name: 'HomeServiceGrid',
		components: {
			FServiceCard
		},
		data() {
			return {
				serviceItems: [
					{
						title: '文档',
						icon: 'compose',
						iconType: 'uni',
						iconColor: '#6698FF',
						path: '/pages/document/index'
					},
					{
						title: '照片',
						icon: 'camera',
						iconType: 'uni',
						iconColor: '#FF6B98',
						path: '/pages/photo/index'
					},
					{
						title: '证件照',
						icon: 'person',
						iconType: 'uni',
						iconColor: '#FFB266',
						path: '/pages/id-photo/index'
					},
					{
						title: '复印',
						icon: 'paperclip',
						iconType: 'uni',
						iconColor: '#66FFB2',
						path: '/pages/copy/index'
					}
				]
			};
		},
		methods: {
			onServiceClick(item) {
				this.$emit('service-click', item);
			}
		}
	}
</script>

<style lang="scss">
	.home-service-grid {
		width: 100%;
		padding: 0;
		
		&__row {
			display: flex;
			margin-bottom: 16rpx;
			
			&:last-child {
				margin-bottom: 0;
			}
		}
		
		&__item {
			flex: 1;
			padding: 0 8rpx;
		}
	}
</style> 