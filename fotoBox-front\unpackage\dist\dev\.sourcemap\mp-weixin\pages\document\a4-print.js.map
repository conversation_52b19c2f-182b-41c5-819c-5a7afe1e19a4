{"version": 3, "file": "a4-print.js", "sources": ["pages/document/a4-print.vue", "C:/Program Files/HBuilderX.4.66.2025051912/plugins/uniapp-cli-vite/uniPage:/cGFnZXMvZG9jdW1lbnQvYTQtcHJpbnQudnVl"], "sourcesContent": ["<template>\n\t<f-page-layout\n\t\ttitle=\"A4文档拍照打印\"\n\t\tfallback-path=\"/pages/document/photo-print\"\n\t\t@back-click=\"onBackClick\"\n\t>\n\t\t<view class=\"a4-print\">\n\t\t\t<view class=\"a4-print__header\">\n\t\t\t\t<text class=\"a4-print__tip\">内容拍照时需要与背景颜色尽量区分</text>\n\t\t\t</view>\n\n\t\t\t<view class=\"a4-print__content\">\n\t\t\t\t<view class=\"a4-print__title\">\n\t\t\t\t\tA4文档拍照打印\n\t\t\t\t</view>\n\n\t\t\t\t<!-- 文档预览区域 -->\n\t\t\t\t<view class=\"a4-print__preview\" @click=\"showActionSheet\">\n\t\t\t\t\t<view class=\"a4-print__document\">\n\t\t\t\t\t\t<!-- 文档内容模拟线条 -->\n\t\t\t\t\t\t<view class=\"a4-print__line\" v-for=\"(item, index) in 15\" :key=\"index\"></view>\n\n\t\t\t\t\t\t<!-- 相机图标 -->\n\t\t\t\t\t\t<view class=\"a4-print__camera\">\n\t\t\t\t\t\t\t<uni-icons type=\"camera\" color=\"#FFFFFF\" size=\"32\"></uni-icons>\n\t\t\t\t\t\t</view>\n\n\t\t\t\t\t\t<!-- 拍摄文档文本 -->\n\t\t\t\t\t\t<view class=\"a4-print__text\">\n\t\t\t\t\t\t\t拍摄文档\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t</view>\n\t\t\n\t\t<!-- 底部操作弹窗 -->\n\t\t<view class=\"action-sheet\" v-if=\"showSheet\" @click=\"hideActionSheet\">\n\t\t\t<view class=\"action-sheet__mask\"></view>\n\t\t\t<view class=\"action-sheet__container\" @click.stop>\n\t\t\t\t<view class=\"action-sheet__item\" @click=\"takePhoto\">拍照</view>\n\t\t\t\t<view class=\"action-sheet__item\" @click=\"chooseFromAlbum\">相册</view>\n\t\t\t\t<view class=\"action-sheet__cancel\" @click=\"hideActionSheet\">取消</view>\n\t\t\t</view>\n\t\t</view>\n\t</f-page-layout>\n</template>\n\n<script>\n\texport default {\n\t\tdata() {\n\t\t\treturn {\n\t\t\t\tshowSheet: false\n\t\t\t};\n\t\t},\n\t\tmethods: {\n\t\t\t// 返回上一页\n\t\t\tonBackClick() {\n\t\t\t\tuni.navigateBack({\n\t\t\t\t\tdelta: 1\n\t\t\t\t});\n\t\t\t},\n\t\t\t\n\t\t\t// 显示操作菜单\n\t\t\tshowActionSheet() {\n\t\t\t\tthis.showSheet = true;\n\t\t\t},\n\t\t\t\n\t\t\t// 隐藏操作菜单\n\t\t\thideActionSheet() {\n\t\t\t\tthis.showSheet = false;\n\t\t\t},\n\t\t\t\n\t\t\t// 拍摄文档\n\t\t\ttakePhoto() {\n\t\t\t\tthis.hideActionSheet();\n\t\t\t\tuni.chooseImage({\n\t\t\t\t\tcount: 1,\n\t\t\t\t\tsourceType: ['camera'],\n\t\t\t\t\tsuccess: (res) => {\n\t\t\t\t\t\tconsole.log('拍摄成功:', res.tempFilePaths);\n\t\t\t\t\t\t// 处理拍摄的图片\n\t\t\t\t\t\tthis.previewPhoto(res.tempFilePaths[0]);\n\t\t\t\t\t},\n\t\t\t\t\tfail: () => {\n\t\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\t\ttitle: '拍摄取消',\n\t\t\t\t\t\t\ticon: 'none'\n\t\t\t\t\t\t});\n\t\t\t\t\t}\n\t\t\t\t});\n\t\t\t},\n\t\t\t\n\t\t\t// 从相册选择\n\t\t\tchooseFromAlbum() {\n\t\t\t\tthis.hideActionSheet();\n\t\t\t\tuni.chooseImage({\n\t\t\t\t\tcount: 1,\n\t\t\t\t\tsourceType: ['album'],\n\t\t\t\t\tsuccess: (res) => {\n\t\t\t\t\t\tconsole.log('选择成功:', res.tempFilePaths);\n\t\t\t\t\t\t// 处理选择的图片\n\t\t\t\t\t\tthis.previewPhoto(res.tempFilePaths[0]);\n\t\t\t\t\t},\n\t\t\t\t\tfail: () => {\n\t\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\t\ttitle: '选择取消',\n\t\t\t\t\t\t\ticon: 'none'\n\t\t\t\t\t\t});\n\t\t\t\t\t}\n\t\t\t\t});\n\t\t\t},\n\t\t\t\n\t\t\t// 预览照片\n\t\t\tpreviewPhoto(path) {\n\t\t\t\t// 这里可以添加预览和编辑功能\n\t\t\t\tuni.showToast({\n\t\t\t\t\ttitle: '文档拍摄成功',\n\t\t\t\t\ticon: 'success'\n\t\t\t\t});\n\t\t\t}\n\t\t}\n\t}\n</script>\n\n<style lang=\"scss\">\n\t.a4-print {\n\t\tbackground: linear-gradient(180deg, #FFE0B2 0%, #FFF8E1 100%); // 渐变背景\n\t\tmin-height: 100vh;\n\t\tdisplay: flex;\n\t\tflex-direction: column;\n\n\t\t&__header {\n\t\t\tbackground-color: #FFD54F; // 更鲜艳的黄色\n\t\t\tpadding: 20rpx;\n\t\t\ttext-align: center;\n\t\t}\n\n\t\t&__tip {\n\t\t\tfont-size: 28rpx;\n\t\t\tcolor: #8D6E63;\n\t\t\tfont-weight: 500;\n\t\t}\n\n\t\t&__content {\n\t\t\tflex: 1;\n\t\t\tdisplay: flex;\n\t\t\tflex-direction: column;\n\t\t\talign-items: center;\n\t\t\tpadding: 40rpx 20rpx;\n\t\t}\n\n\t\t&__title {\n\t\t\tfont-size: 48rpx;\n\t\t\tfont-weight: bold;\n\t\t\tcolor: #FF8F00; // 更鲜艳的橙色\n\t\t\tmargin-bottom: 60rpx;\n\t\t\ttext-align: center;\n\t\t\ttext-shadow: 0 2rpx 4rpx rgba(255, 143, 0, 0.3);\n\t\t}\n\t\t\n\t\t&__preview {\n\t\t\twidth: 100%;\n\t\t\tdisplay: flex;\n\t\t\tjustify-content: center;\n\t\t\tpadding: 0 40rpx;\n\t\t}\n\n\t\t&__document {\n\t\t\twidth: 100%;\n\t\t\tmax-width: 600rpx;\n\t\t\taspect-ratio: 1 / 1.414; // A4纸张比例\n\t\t\tbackground-color: #fff;\n\t\t\tborder-radius: 16rpx;\n\t\t\tbox-shadow: 0 8rpx 24rpx rgba(0, 0, 0, 0.12);\n\t\t\tpadding: 50rpx 40rpx;\n\t\t\tdisplay: flex;\n\t\t\tflex-direction: column;\n\t\t\tposition: relative;\n\t\t\toverflow: hidden;\n\t\t\tborder: 2rpx solid #FFE0B2;\n\t\t}\n\n\t\t&__line {\n\t\t\theight: 18rpx;\n\t\t\tbackground-color: #FFD54F; // 更鲜艳的黄色线条\n\t\t\tmargin-bottom: 28rpx;\n\t\t\tborder-radius: 9rpx;\n\t\t\topacity: 0.8;\n\n\t\t\t&:nth-child(1) { width: 95%; }\n\t\t\t&:nth-child(2) { width: 85%; }\n\t\t\t&:nth-child(3) { width: 70%; }\n\t\t\t&:nth-child(4) { width: 90%; }\n\t\t\t&:nth-child(5) { width: 100%; }\n\t\t\t&:nth-child(6) { width: 75%; }\n\t\t\t&:nth-child(7) { width: 100%; }\n\t\t\t&:nth-child(8) { width: 80%; }\n\t\t\t&:nth-child(9) { width: 65%; }\n\t\t\t&:nth-child(10) { width: 95%; }\n\t\t\t&:nth-child(11) { width: 85%; }\n\t\t\t&:nth-child(12) { width: 100%; }\n\t\t\t&:nth-child(13) { width: 70%; }\n\t\t\t&:nth-child(14) { width: 90%; }\n\t\t\t&:nth-child(15) { width: 60%; }\n\t\t}\n\t\t\n\t\t&__camera {\n\t\t\tposition: absolute;\n\t\t\ttop: 50%;\n\t\t\tleft: 50%;\n\t\t\ttransform: translate(-50%, -50%);\n\t\t\twidth: 140rpx;\n\t\t\theight: 140rpx;\n\t\t\tbackground-color: rgba(0, 0, 0, 0.6);\n\t\t\tborder-radius: 50%;\n\t\t\tdisplay: flex;\n\t\t\talign-items: center;\n\t\t\tjustify-content: center;\n\t\t\tbox-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.3);\n\t\t}\n\n\t\t&__text {\n\t\t\tposition: absolute;\n\t\t\tbottom: 80rpx;\n\t\t\tleft: 0;\n\t\t\twidth: 100%;\n\t\t\ttext-align: center;\n\t\t\tfont-size: 36rpx;\n\t\t\tcolor: #757575;\n\t\t\tfont-weight: 500;\n\t\t}\n\t}\n\t\n\t/* 底部操作菜单 */\n\t.action-sheet {\n\t\tposition: fixed;\n\t\ttop: 0;\n\t\tright: 0;\n\t\tbottom: 0;\n\t\tleft: 0;\n\t\tz-index: 999;\n\t\t\n\t\t&__mask {\n\t\t\tposition: absolute;\n\t\t\ttop: 0;\n\t\t\tright: 0;\n\t\t\tbottom: 0;\n\t\t\tleft: 0;\n\t\t\tbackground-color: rgba(0, 0, 0, 0.5);\n\t\t}\n\t\t\n\t\t&__container {\n\t\t\tposition: absolute;\n\t\t\tbottom: 0;\n\t\t\tleft: 0;\n\t\t\twidth: 100%;\n\t\t\tbackground-color: #fff;\n\t\t\tborder-top-left-radius: 24rpx;\n\t\t\tborder-top-right-radius: 24rpx;\n\t\t\toverflow: hidden;\n\t\t\ttransform: translateY(0);\n\t\t\ttransition: transform 0.3s;\n\t\t}\n\t\t\n\t\t&__item {\n\t\t\theight: 110rpx;\n\t\t\tline-height: 110rpx;\n\t\t\ttext-align: center;\n\t\t\tfont-size: 32rpx;\n\t\t\tcolor: #333;\n\t\t\tborder-bottom: 1px solid #f5f5f5;\n\t\t\t\n\t\t\t&:active {\n\t\t\t\tbackground-color: #f9f9f9;\n\t\t\t}\n\t\t}\n\t\t\n\t\t&__cancel {\n\t\t\theight: 110rpx;\n\t\t\tline-height: 110rpx;\n\t\t\ttext-align: center;\n\t\t\tfont-size: 32rpx;\n\t\t\tcolor: #333;\n\t\t\tmargin-top: 16rpx;\n\t\t\tbackground-color: #fff;\n\t\t\t\n\t\t\t&:active {\n\t\t\t\tbackground-color: #f9f9f9;\n\t\t\t}\n\t\t}\n\t}\n</style> ", "import MiniProgramPage from 'D:/Yunchuang/PhotoFront/fotoBox-front/pages/document/a4-print.vue'\nwx.createPage(MiniProgramPage)"], "names": ["uni"], "mappings": ";;AAiDC,MAAK,YAAU;AAAA,EACd,OAAO;AACN,WAAO;AAAA,MACN,WAAW;AAAA;EAEZ;AAAA,EACD,SAAS;AAAA;AAAA,IAER,cAAc;AACbA,oBAAAA,MAAI,aAAa;AAAA,QAChB,OAAO;AAAA,MACR,CAAC;AAAA,IACD;AAAA;AAAA,IAGD,kBAAkB;AACjB,WAAK,YAAY;AAAA,IACjB;AAAA;AAAA,IAGD,kBAAkB;AACjB,WAAK,YAAY;AAAA,IACjB;AAAA;AAAA,IAGD,YAAY;AACX,WAAK,gBAAe;AACpBA,oBAAAA,MAAI,YAAY;AAAA,QACf,OAAO;AAAA,QACP,YAAY,CAAC,QAAQ;AAAA,QACrB,SAAS,CAAC,QAAQ;AACjBA,wBAAY,MAAA,MAAA,OAAA,qCAAA,SAAS,IAAI,aAAa;AAEtC,eAAK,aAAa,IAAI,cAAc,CAAC,CAAC;AAAA,QACtC;AAAA,QACD,MAAM,MAAM;AACXA,wBAAAA,MAAI,UAAU;AAAA,YACb,OAAO;AAAA,YACP,MAAM;AAAA,UACP,CAAC;AAAA,QACF;AAAA,MACD,CAAC;AAAA,IACD;AAAA;AAAA,IAGD,kBAAkB;AACjB,WAAK,gBAAe;AACpBA,oBAAAA,MAAI,YAAY;AAAA,QACf,OAAO;AAAA,QACP,YAAY,CAAC,OAAO;AAAA,QACpB,SAAS,CAAC,QAAQ;AACjBA,wBAAY,MAAA,MAAA,OAAA,sCAAA,SAAS,IAAI,aAAa;AAEtC,eAAK,aAAa,IAAI,cAAc,CAAC,CAAC;AAAA,QACtC;AAAA,QACD,MAAM,MAAM;AACXA,wBAAAA,MAAI,UAAU;AAAA,YACb,OAAO;AAAA,YACP,MAAM;AAAA,UACP,CAAC;AAAA,QACF;AAAA,MACD,CAAC;AAAA,IACD;AAAA;AAAA,IAGD,aAAa,MAAM;AAElBA,oBAAAA,MAAI,UAAU;AAAA,QACb,OAAO;AAAA,QACP,MAAM;AAAA,MACP,CAAC;AAAA,IACF;AAAA,EACD;AACD;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACzHD,GAAG,WAAW,eAAe;"}