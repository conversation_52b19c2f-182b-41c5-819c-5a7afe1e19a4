"use strict";
const common_vendor = require("../../common/vendor.js");
const _sfc_main = {
  data() {
    return {
      // 数据
    };
  },
  methods: {
    // 返回上一页
    onBackClick() {
      common_vendor.index.__f__("log", "at pages/document/photo-print.vue:107", "返回上一页");
    },
    // 处理A4文档打印
    handleA4Print() {
      common_vendor.index.__f__("log", "at pages/document/photo-print.vue:111", "点击了A4文档");
      common_vendor.index.navigateTo({
        url: "/pages/copy/a4-print"
      });
    },
    // 处理身份证/居住证打印
    handleIdCardPrint() {
      common_vendor.index.__f__("log", "at pages/document/photo-print.vue:119", "点击了身份证/居住证");
      common_vendor.index.navigateTo({
        url: "/pages/copy/id-card-print"
      });
    },
    // 处理户口本打印
    handleHouseholdPrint() {
      common_vendor.index.__f__("log", "at pages/document/photo-print.vue:126", "点击了户口本");
      common_vendor.index.navigateTo({
        url: "/pages/copy/household-print"
      });
    },
    // 处理营业执照打印
    handleBusinessLicensePrint() {
      common_vendor.index.__f__("log", "at pages/document/photo-print.vue:133", "点击了营业执照");
      common_vendor.index.navigateTo({
        url: "/pages/copy/business-license-print"
      });
    },
    // 处理驾驶证/行驶证打印
    handleDrivingLicensePrint() {
      common_vendor.index.__f__("log", "at pages/document/photo-print.vue:140", "点击了驾驶证/行驶证");
      common_vendor.index.navigateTo({
        url: "/pages/copy/driving-license-print"
      });
    },
    // 处理银行卡/社保卡打印
    handleBankCardPrint() {
      common_vendor.index.__f__("log", "at pages/document/photo-print.vue:147", "点击了银行卡/社保卡");
      common_vendor.index.navigateTo({
        url: "/pages/copy/bank-card-print"
      });
    }
  }
};
if (!Array) {
  const _easycom_uni_icons2 = common_vendor.resolveComponent("uni-icons");
  const _component_f_page_layout = common_vendor.resolveComponent("f-page-layout");
  (_easycom_uni_icons2 + _component_f_page_layout)();
}
const _easycom_uni_icons = () => "../../uni_modules/uni-icons/components/uni-icons/uni-icons.js";
if (!Math) {
  _easycom_uni_icons();
}
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return {
    a: common_vendor.p({
      type: "paperclip",
      color: "#FFFFFF",
      size: "22"
    }),
    b: common_vendor.o((...args) => $options.handleA4Print && $options.handleA4Print(...args)),
    c: common_vendor.p({
      type: "person",
      color: "#FFFFFF",
      size: "22"
    }),
    d: common_vendor.o((...args) => $options.handleIdCardPrint && $options.handleIdCardPrint(...args)),
    e: common_vendor.p({
      type: "list",
      color: "#FFFFFF",
      size: "22"
    }),
    f: common_vendor.o((...args) => $options.handleHouseholdPrint && $options.handleHouseholdPrint(...args)),
    g: common_vendor.p({
      type: "shop",
      color: "#FFFFFF",
      size: "22"
    }),
    h: common_vendor.o((...args) => $options.handleBusinessLicensePrint && $options.handleBusinessLicensePrint(...args)),
    i: common_vendor.p({
      type: "navigate",
      color: "#FFFFFF",
      size: "22"
    }),
    j: common_vendor.o((...args) => $options.handleDrivingLicensePrint && $options.handleDrivingLicensePrint(...args)),
    k: common_vendor.p({
      type: "wallet",
      color: "#FFFFFF",
      size: "22"
    }),
    l: common_vendor.o((...args) => $options.handleBankCardPrint && $options.handleBankCardPrint(...args)),
    m: common_vendor.o($options.onBackClick),
    n: common_vendor.p({
      title: "拍照打印",
      ["fallback-path"]: "/pages/document/index"
    })
  };
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages/document/photo-print.js.map
