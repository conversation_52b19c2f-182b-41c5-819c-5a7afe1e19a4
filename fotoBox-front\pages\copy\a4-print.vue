<template>
	<f-page-layout 
		title="A4文档拍照打印" 
		fallback-path="/pages/copy/index"
		@back-click="onBackClick"
	>
		<view class="a4-print">
			<view class="a4-print__header">
				<text class="a4-print__tip">内容拍照时需要与背景颜色尽量区分</text>
			</view>
			
			<view class="a4-print__content">
				<view class="a4-print__title">
					A4文档拍照打印
				</view>
				
				<!-- 文档预览区域 -->
				<view class="a4-print__preview" @click="showActionSheet">
					<view class="a4-print__document">
						<!-- 文档内容模拟线条 -->
						<view class="a4-print__line" v-for="(item, index) in 12" :key="index"></view>
						
						<!-- 相机图标 -->
						<view class="a4-print__camera">
							<uni-icons type="camera" color="#FFFFFF" size="30"></uni-icons>
						</view>
						
						<!-- 拍摄文档文本 -->
						<view class="a4-print__text">
							拍摄文档
						</view>
					</view>
				</view>
			</view>
		</view>
		
		<!-- 底部操作弹窗 -->
		<view class="action-sheet" v-if="showSheet" @click="hideActionSheet">
			<view class="action-sheet__mask"></view>
			<view class="action-sheet__container" @click.stop>
				<view class="action-sheet__item" @click="takePhoto">拍照</view>
				<view class="action-sheet__item" @click="chooseFromAlbum">相册</view>
				<view class="action-sheet__cancel" @click="hideActionSheet">取消</view>
			</view>
		</view>
	</f-page-layout>
</template>

<script>
	export default {
		data() {
			return {
				showSheet: false
			};
		},
		methods: {
			// 返回上一页
			onBackClick() {
				uni.navigateBack({
					delta: 1
				});
			},
			
			// 显示操作菜单
			showActionSheet() {
				this.showSheet = true;
			},
			
			// 隐藏操作菜单
			hideActionSheet() {
				this.showSheet = false;
			},
			
			// 拍摄文档
			takePhoto() {
				this.hideActionSheet();
				uni.chooseImage({
					count: 1,
					sourceType: ['camera'],
					success: (res) => {
						console.log('拍摄成功:', res.tempFilePaths);
						// 处理拍摄的图片
						this.previewPhoto(res.tempFilePaths[0]);
					},
					fail: () => {
						uni.showToast({
							title: '拍摄取消',
							icon: 'none'
						});
					}
				});
			},
			
			// 从相册选择
			chooseFromAlbum() {
				this.hideActionSheet();
				uni.chooseImage({
					count: 1,
					sourceType: ['album'],
					success: (res) => {
						console.log('选择成功:', res.tempFilePaths);
						// 处理选择的图片
						this.previewPhoto(res.tempFilePaths[0]);
					},
					fail: () => {
						uni.showToast({
							title: '选择取消',
							icon: 'none'
						});
					}
				});
			},
			
			// 预览照片
			previewPhoto(path) {
				// 这里可以添加预览和编辑功能
				uni.showToast({
					title: '文档拍摄成功',
					icon: 'success'
				});
			}
		}
	}
</script>

<style lang="scss">
	.a4-print {
		background-color: #FFF8E1; // 更准确的浅黄色背景
		min-height: 100vh;
		display: flex;
		flex-direction: column;
		
		&__header {
			background-color: #FFD180; // 更准确的橙黄色
			padding: 16rpx;
			text-align: center;
		}
		
		&__tip {
			font-size: 26rpx;
			color: #795548;
		}
		
		&__content {
			flex: 1;
			display: flex;
			flex-direction: column;
			align-items: center;
			padding: 30rpx 20rpx;
		}
		
		&__title {
			font-size: 46rpx;
			font-weight: bold;
			color: #FFA000; // 更深的橙色
			margin-bottom: 50rpx;
			text-align: center;
		}
		
		&__preview {
			width: 100%;
			display: flex;
			justify-content: center;
			padding: 0 30rpx;
		}
		
		&__document {
			width: 100%;
			max-width: 550rpx;
			aspect-ratio: 1 / 1.414; // A4纸张比例
			background-color: #fff;
			border-radius: 12rpx;
			box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.08);
			padding: 40rpx 30rpx;
			display: flex;
			flex-direction: column;
			position: relative;
			overflow: hidden;
		}
		
		&__line {
			height: 16rpx;
			background-color: #FFE0B2; // 更浅的橙黄色，与背景更协调
			margin-bottom: 24rpx;
			border-radius: 8rpx;
			
			&:nth-child(odd) {
				width: 100%;
			}
			
			&:nth-child(even) {
				width: 80%;
			}
			
			&:nth-child(3n) {
				width: 60%;
			}
		}
		
		&__camera {
			position: absolute;
			top: 50%;
			left: 50%;
			transform: translate(-50%, -50%);
			width: 120rpx;
			height: 120rpx;
			background-color: rgba(0, 0, 0, 0.5); // 更透明一些
			border-radius: 50%;
			display: flex;
			align-items: center;
			justify-content: center;
		}
		
		&__text {
			position: absolute;
			bottom: 60rpx;
			left: 0;
			width: 100%;
			text-align: center;
			font-size: 32rpx;
			color: #9E9E9E; // 更准确的灰色
		}
	}
	
	/* 底部操作菜单 */
	.action-sheet {
		position: fixed;
		top: 0;
		right: 0;
		bottom: 0;
		left: 0;
		z-index: 999;
		
		&__mask {
			position: absolute;
			top: 0;
			right: 0;
			bottom: 0;
			left: 0;
			background-color: rgba(0, 0, 0, 0.5);
		}
		
		&__container {
			position: absolute;
			bottom: 0;
			left: 0;
			width: 100%;
			background-color: #fff;
			border-top-left-radius: 24rpx;
			border-top-right-radius: 24rpx;
			overflow: hidden;
			transform: translateY(0);
			transition: transform 0.3s;
		}
		
		&__item {
			height: 110rpx;
			line-height: 110rpx;
			text-align: center;
			font-size: 32rpx;
			color: #333;
			border-bottom: 1px solid #f5f5f5;
			
			&:active {
				background-color: #f9f9f9;
			}
		}
		
		&__cancel {
			height: 110rpx;
			line-height: 110rpx;
			text-align: center;
			font-size: 32rpx;
			color: #333;
			margin-top: 16rpx;
			background-color: #fff;
			
			&:active {
				background-color: #f9f9f9;
			}
		}
	}
</style> 