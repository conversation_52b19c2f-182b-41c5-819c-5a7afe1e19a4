{"version": 3, "file": "five-inch-photo.js", "sources": ["pages/photo/five-inch-photo.vue", "C:/Program Files/HBuilderX.4.66.2025051912/plugins/uniapp-cli-vite/uniPage:/cGFnZXMvcGhvdG8vZml2ZS1pbmNoLXBob3RvLnZ1ZQ"], "sourcesContent": ["<template>\n\t<f-page-layout title=\"5寸照片\" back>\n\t\t<view class=\"five-inch-photo\">\n\t\t\t<!-- 空状态展示 -->\n\t\t\t<view class=\"empty-state\" v-if=\"photoList.length === 0\">\n\t\t\t\t<image class=\"empty-box\" src=\"/static/images/icons/Null.png\" mode=\"aspectFit\"></image>\n\t\t\t\t<text class=\"empty-text\">暂无照片哦~</text>\n\t\t\t</view>\n\t\t\t\n\t\t\t<!-- 照片列表 -->\n\t\t\t<view class=\"file-list-container\" v-else>\n\t\t\t\t<scroll-view class=\"file-list-scroll\" scroll-y>\n\t\t\t\t\t<view class=\"file-list\">\n\t\t\t\t\t\t<view class=\"file-item\" v-for=\"(photo, index) in photoList\" :key=\"index\">\n\t\t\t\t\t\t\t<!-- 上传中状态 -->\n\t\t\t\t\t\t\t<view class=\"file-card uploading\" v-if=\"photo.uploading\">\n\t\t\t\t\t\t\t\t<view class=\"file-name\">{{photo.name || '5寸照片_' + (index + 1)}}</view>\n\t\t\t\t\t\t\t\t<view class=\"upload-progress-text\">照片上传中</view>\n\t\t\t\t\t\t\t\t<view class=\"upload-progress-bar-container\">\n\t\t\t\t\t\t\t\t\t<view class=\"upload-progress-bar\" :style=\"{width: photo.progress + '%'}\"></view>\n\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t\t<view class=\"upload-progress-value\">{{photo.progress}}%</view>\n\t\t\t\t\t\t\t\t<view class=\"delete-btn\" @click.stop=\"deletePhoto(index)\">\n\t\t\t\t\t\t\t\t\t<uni-icons type=\"trash\" size=\"24\" color=\"#333333\"></uni-icons>\n\t\t\t\t\t\t\t\t\t<text>删除</text>\n\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t\n\t\t\t\t\t\t\t<!-- 上传完成状态 - 打印设置卡片 -->\n\t\t\t\t\t\t\t<view class=\"file-card print-settings\" v-else>\n\t\t\t\t\t\t\t\t<view class=\"file-header\">\n\t\t\t\t\t\t\t\t\t<text class=\"file-name\">{{photo.name || '5寸照片_' + (index + 1)}}</text>\n\t\t\t\t\t\t\t\t\t<image class=\"photo-thumbnail\" :src=\"photo.path\" mode=\"aspectFill\"></image>\n\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t\t\n\t\t\t\t\t\t\t\t<!-- 打印设置选项 -->\n\t\t\t\t\t\t\t\t<view class=\"setting-item\">\n\t\t\t\t\t\t\t\t\t<text class=\"setting-label\">打印份数</text>\n\t\t\t\t\t\t\t\t\t<view class=\"copies-selector\">\n\t\t\t\t\t\t\t\t\t\t<view class=\"copies-btn\" @click=\"decreaseCopies(index)\">-</view>\n\t\t\t\t\t\t\t\t\t\t<input class=\"copies-input\" type=\"number\" v-model=\"photo.copies\" />\n\t\t\t\t\t\t\t\t\t\t<view class=\"copies-btn\" @click=\"increaseCopies(index)\">+</view>\n\t\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t\t\n\t\t\t\t\t\t\t\t<view class=\"setting-item\">\n\t\t\t\t\t\t\t\t\t<text class=\"setting-label\">打印页面</text>\n\t\t\t\t\t\t\t\t\t<view class=\"option-selector\">\n\t\t\t\t\t\t\t\t\t\t<view class=\"option-btn\" :class=\"{'active': photo.printMode === 'single'}\" @click=\"setPrintMode(index, 'single')\">\n\t\t\t\t\t\t\t\t\t\t\t单面\n\t\t\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t\t\t\t<view class=\"option-btn\" :class=\"{'active': photo.printMode === 'double'}\" @click=\"setPrintMode(index, 'double')\">\n\t\t\t\t\t\t\t\t\t\t\t双面\n\t\t\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t\t\n\t\t\t\t\t\t\t\t<view class=\"setting-item\">\n\t\t\t\t\t\t\t\t\t<text class=\"setting-label\">打印类型</text>\n\t\t\t\t\t\t\t\t\t<view class=\"option-selector\">\n\t\t\t\t\t\t\t\t\t\t<view class=\"option-btn\" :class=\"{'active': photo.printType === 'black-white'}\" @click=\"setPrintType(index, 'black-white')\">\n\t\t\t\t\t\t\t\t\t\t\t黑白\n\t\t\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t\t\t\t<view class=\"option-btn\" :class=\"{'active': photo.printType === 'color'}\" @click=\"setPrintType(index, 'color')\">\n\t\t\t\t\t\t\t\t\t\t\t彩色\n\t\t\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t\t\n\t\t\t\t\t\t\t\t<view class=\"file-actions\">\n\t\t\t\t\t\t\t\t\t<view class=\"action-btn\" @click=\"previewPhoto(photo)\">\n\t\t\t\t\t\t\t\t\t\t<uni-icons type=\"eye\" size=\"20\" color=\"#FFA500\"></uni-icons>\n\t\t\t\t\t\t\t\t\t\t<text>预览</text>\n\t\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t\t\t<view class=\"action-btn\" @click=\"deletePhoto(index)\">\n\t\t\t\t\t\t\t\t\t\t<uni-icons type=\"trash\" size=\"20\" color=\"#333333\"></uni-icons>\n\t\t\t\t\t\t\t\t\t\t<text>删除</text>\n\t\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</view>\n\t\t\t\t</scroll-view>\n\t\t\t</view>\n\t\t\t\n\t\t\t<!-- 底部固定区域 -->\n\t\t\t<view class=\"fixed-bottom-area\">\n\t\t\t\t<view class=\"notice-text\">\n\t\t\t\t\t严禁打印涉及反动、淫秽、暴恐、色情、侵权等非法内容\n\t\t\t\t</view>\n\t\t\t\t\n\t\t\t\t<view class=\"btn-group\">\n\t\t\t\t\t<button class=\"select-btn\" @click=\"addPhoto\">添加照片</button>\n\t\t\t\t\t<button class=\"print-btn\" @click=\"goPrint\" :disabled=\"photoList.length === 0\">下单打印</button>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t</view>\n\t</f-page-layout>\n</template>\n\n<script>\nexport default {\n\tdata() {\n\t\treturn {\n\t\t\tphotoList: []\n\t\t}\n\t},\n\t\n\tmethods: {\n\t\t// 添加照片 - 支持拍照和相册选择\n\t\taddPhoto() {\n\t\t\tuni.showActionSheet({\n\t\t\t\titemList: ['拍照', '从相册选择'],\n\t\t\t\tsuccess: (res) => {\n\t\t\t\t\tif (res.tapIndex === 0) {\n\t\t\t\t\t\tthis.takePhoto();\n\t\t\t\t\t} else if (res.tapIndex === 1) {\n\t\t\t\t\t\tthis.selectPhotoFromAlbum();\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t});\n\t\t},\n\t\t\n\t\t// 拍照\n\t\ttakePhoto() {\n\t\t\tuni.chooseImage({\n\t\t\t\tcount: 1,\n\t\t\t\tsourceType: ['camera'],\n\t\t\t\tsuccess: (res) => {\n\t\t\t\t\tthis.handleSelectedPhotos(res.tempFilePaths);\n\t\t\t\t},\n\t\t\t\tfail: (err) => {\n\t\t\t\t\tconsole.error('拍照失败:', err);\n\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\ttitle: '拍照失败',\n\t\t\t\t\t\ticon: 'none'\n\t\t\t\t\t});\n\t\t\t\t}\n\t\t\t});\n\t\t},\n\t\t\n\t\t// 选择手机相册照片\n\t\tselectPhotoFromAlbum() {\n\t\t\tuni.chooseImage({\n\t\t\t\tcount: 9, // 最多选择9张\n\t\t\t\tsourceType: ['album'],\n\t\t\t\tsuccess: (res) => {\n\t\t\t\t\tthis.handleSelectedPhotos(res.tempFilePaths);\n\t\t\t\t},\n\t\t\t\tfail: (err) => {\n\t\t\t\t\tconsole.error('选择照片失败:', err);\n\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\ttitle: '选择照片失败',\n\t\t\t\t\t\ticon: 'none'\n\t\t\t\t\t});\n\t\t\t\t}\n\t\t\t});\n\t\t},\n\t\t\n\t\t// 处理选择的照片\n\t\thandleSelectedPhotos(filePaths) {\n\t\t\tfilePaths.forEach((path, index) => {\n\t\t\t\tconst photo = {\n\t\t\t\t\tpath: path,\n\t\t\t\t\tname: `5寸照片_${this.photoList.length + index + 1}`,\n\t\t\t\t\tcopies: 1,\n\t\t\t\t\tprintMode: 'single',\n\t\t\t\t\tprintType: 'color',\n\t\t\t\t\tuploading: true,\n\t\t\t\t\tprogress: 0\n\t\t\t\t};\n\t\t\t\t\n\t\t\t\tthis.photoList.push(photo);\n\t\t\t\tthis.simulateUpload(this.photoList.length - 1);\n\t\t\t});\n\t\t},\n\t\t\n\t\t// 模拟上传进度\n\t\tsimulateUpload(index) {\n\t\t\tconst photo = this.photoList[index];\n\t\t\tconst interval = setInterval(() => {\n\t\t\t\tphoto.progress += Math.random() * 20;\n\t\t\t\tif (photo.progress >= 100) {\n\t\t\t\t\tphoto.progress = 100;\n\t\t\t\t\tphoto.uploading = false;\n\t\t\t\t\tclearInterval(interval);\n\t\t\t\t}\n\t\t\t}, 200);\n\t\t},\n\t\t\n\t\t// 增加份数\n\t\tincreaseCopies(index) {\n\t\t\tif (this.photoList[index].copies < 99) {\n\t\t\t\tthis.photoList[index].copies++;\n\t\t\t}\n\t\t},\n\t\t\n\t\t// 减少份数\n\t\tdecreaseCopies(index) {\n\t\t\tif (this.photoList[index].copies > 1) {\n\t\t\t\tthis.photoList[index].copies--;\n\t\t\t}\n\t\t},\n\t\t\n\t\t// 设置打印模式\n\t\tsetPrintMode(index, mode) {\n\t\t\tthis.photoList[index].printMode = mode;\n\t\t},\n\t\t\n\t\t// 设置打印类型\n\t\tsetPrintType(index, type) {\n\t\t\tthis.photoList[index].printType = type;\n\t\t},\n\t\t\n\t\t// 预览照片\n\t\tpreviewPhoto(photo) {\n\t\t\tuni.previewImage({\n\t\t\t\turls: [photo.path],\n\t\t\t\tcurrent: photo.path\n\t\t\t});\n\t\t},\n\t\t\n\t\t// 删除照片\n\t\tdeletePhoto(index) {\n\t\t\tuni.showModal({\n\t\t\t\ttitle: '确认删除',\n\t\t\t\tcontent: '确定要删除这张照片吗？',\n\t\t\t\tsuccess: (res) => {\n\t\t\t\t\tif (res.confirm) {\n\t\t\t\t\t\tthis.photoList.splice(index, 1);\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t});\n\t\t},\n\t\t\n\t\t// 去打印\n\t\tgoPrint() {\n\t\t\tif (this.photoList.length === 0) {\n\t\t\t\tuni.showToast({\n\t\t\t\t\ttitle: '请先添加照片',\n\t\t\t\t\ticon: 'none'\n\t\t\t\t});\n\t\t\t\treturn;\n\t\t\t}\n\t\t\t\n\t\t\t// 检查是否有正在上传的照片\n\t\t\tconst uploadingPhotos = this.photoList.filter(photo => photo.uploading);\n\t\t\tif (uploadingPhotos.length > 0) {\n\t\t\t\tuni.showToast({\n\t\t\t\t\ttitle: '请等待照片上传完成',\n\t\t\t\t\ticon: 'none'\n\t\t\t\t});\n\t\t\t\treturn;\n\t\t\t}\n\t\t\t\n\t\t\t// 计算总份数和价格\n\t\t\tconst totalCopies = this.photoList.reduce((sum, photo) => sum + photo.copies, 0);\n\t\t\tconst pricePerCopy = 1.5; // 5寸照片单价\n\t\t\tconst totalPrice = totalCopies * pricePerCopy;\n\t\t\t\n\t\t\t// 显示打印确认信息\n\t\t\tuni.showModal({\n\t\t\t\ttitle: '确认打印',\n\t\t\t\tcontent: `共${this.photoList.length}张照片，${totalCopies}份，预计费用：¥${totalPrice.toFixed(2)}`,\n\t\t\t\tconfirmText: '确认打印',\n\t\t\t\tcancelText: '取消',\n\t\t\t\tsuccess: (res) => {\n\t\t\t\t\tif (res.confirm) {\n\t\t\t\t\t\t// 这里可以调用打印API或跳转到支付页面\n\t\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\t\ttitle: '打印订单已提交',\n\t\t\t\t\t\t\ticon: 'success'\n\t\t\t\t\t\t});\n\t\t\t\t\t\t\n\t\t\t\t\t\t// 可以跳转到订单页面\n\t\t\t\t\t\tsetTimeout(() => {\n\t\t\t\t\t\t\tuni.navigateBack();\n\t\t\t\t\t\t}, 1500);\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t});\n\t\t}\n\t}\n}\n</script>\n\n<style lang=\"scss\" scoped>\n.five-inch-photo {\n\tbackground-color: #f5f5f5;\n\tmin-height: 100vh;\n\tpadding-bottom: 200rpx; // 为底部固定区域留出空间\n\n\t.empty-state {\n\t\tdisplay: flex;\n\t\tflex-direction: column;\n\t\talign-items: center;\n\t\tjustify-content: center;\n\t\tpadding: 200rpx 40rpx;\n\n\t\t.empty-box {\n\t\t\twidth: 200rpx;\n\t\t\theight: 200rpx;\n\t\t\tmargin-bottom: 40rpx;\n\t\t}\n\n\t\t.empty-text {\n\t\t\tfont-size: 28rpx;\n\t\t\tcolor: #999999;\n\t\t}\n\t}\n\n\t.file-list-container {\n\t\theight: calc(100vh - 200rpx);\n\n\t\t.file-list-scroll {\n\t\t\theight: 100%;\n\n\t\t\t.file-list {\n\t\t\t\tpadding: 20rpx;\n\n\t\t\t\t.file-item {\n\t\t\t\t\tmargin-bottom: 20rpx;\n\n\t\t\t\t\t.file-card {\n\t\t\t\t\t\tbackground-color: #ffffff;\n\t\t\t\t\t\tborder-radius: 12rpx;\n\t\t\t\t\t\tpadding: 30rpx;\n\t\t\t\t\t\tbox-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);\n\n\t\t\t\t\t\t&.uploading {\n\t\t\t\t\t\t\t.file-name {\n\t\t\t\t\t\t\t\tfont-size: 28rpx;\n\t\t\t\t\t\t\t\tcolor: #333;\n\t\t\t\t\t\t\t\tfont-weight: 500;\n\t\t\t\t\t\t\t\tmargin-bottom: 20rpx;\n\t\t\t\t\t\t\t}\n\n\t\t\t\t\t\t\t.upload-progress-text {\n\t\t\t\t\t\t\t\tfont-size: 24rpx;\n\t\t\t\t\t\t\t\tcolor: #666;\n\t\t\t\t\t\t\t\tmargin-bottom: 10rpx;\n\t\t\t\t\t\t\t}\n\n\t\t\t\t\t\t\t.upload-progress-bar-container {\n\t\t\t\t\t\t\t\twidth: 100%;\n\t\t\t\t\t\t\t\theight: 8rpx;\n\t\t\t\t\t\t\t\tbackground-color: #f0f0f0;\n\t\t\t\t\t\t\t\tborder-radius: 4rpx;\n\t\t\t\t\t\t\t\tmargin-bottom: 10rpx;\n\t\t\t\t\t\t\t\toverflow: hidden;\n\n\t\t\t\t\t\t\t\t.upload-progress-bar {\n\t\t\t\t\t\t\t\t\theight: 100%;\n\t\t\t\t\t\t\t\t\tbackground-color: #007aff;\n\t\t\t\t\t\t\t\t\tborder-radius: 4rpx;\n\t\t\t\t\t\t\t\t\ttransition: width 0.3s ease;\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t}\n\n\t\t\t\t\t\t\t.upload-progress-value {\n\t\t\t\t\t\t\t\tfont-size: 24rpx;\n\t\t\t\t\t\t\t\tcolor: #007aff;\n\t\t\t\t\t\t\t\ttext-align: right;\n\t\t\t\t\t\t\t\tmargin-bottom: 20rpx;\n\t\t\t\t\t\t\t}\n\n\t\t\t\t\t\t\t.delete-btn {\n\t\t\t\t\t\t\t\tdisplay: flex;\n\t\t\t\t\t\t\t\tflex-direction: column;\n\t\t\t\t\t\t\t\talign-items: center;\n\t\t\t\t\t\t\t\tjustify-content: center;\n\t\t\t\t\t\t\t\twidth: 100rpx;\n\t\t\t\t\t\t\t\theight: 80rpx;\n\t\t\t\t\t\t\t\tmargin-left: auto;\n\n\t\t\t\t\t\t\t\ttext {\n\t\t\t\t\t\t\t\t\tfont-size: 24rpx;\n\t\t\t\t\t\t\t\t\tcolor: #333;\n\t\t\t\t\t\t\t\t\tmargin-top: 4rpx;\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t}\n\n\t\t\t\t\t\t&.print-settings {\n\t\t\t\t\t\t\t.file-header {\n\t\t\t\t\t\t\t\tdisplay: flex;\n\t\t\t\t\t\t\t\tjustify-content: space-between;\n\t\t\t\t\t\t\t\talign-items: center;\n\t\t\t\t\t\t\t\tpadding-bottom: 20rpx;\n\t\t\t\t\t\t\t\tborder-bottom: 1px solid #f0f0f0;\n\n\t\t\t\t\t\t\t\t.file-name {\n\t\t\t\t\t\t\t\t\tfont-size: 28rpx;\n\t\t\t\t\t\t\t\t\tcolor: #333;\n\t\t\t\t\t\t\t\t\tfont-weight: 500;\n\t\t\t\t\t\t\t\t\tmax-width: 70%;\n\t\t\t\t\t\t\t\t\twhite-space: nowrap;\n\t\t\t\t\t\t\t\t\toverflow: hidden;\n\t\t\t\t\t\t\t\t\ttext-overflow: ellipsis;\n\t\t\t\t\t\t\t\t}\n\n\t\t\t\t\t\t\t\t.photo-thumbnail {\n\t\t\t\t\t\t\t\t\twidth: 80rpx;\n\t\t\t\t\t\t\t\t\theight: 80rpx;\n\t\t\t\t\t\t\t\t\tborder-radius: 6rpx;\n\t\t\t\t\t\t\t\t\tobject-fit: cover;\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t}\n\n\t\t\t\t\t\t\t.setting-item {\n\t\t\t\t\t\t\t\tdisplay: flex;\n\t\t\t\t\t\t\t\tjustify-content: space-between;\n\t\t\t\t\t\t\t\talign-items: center;\n\t\t\t\t\t\t\t\tpadding: 20rpx 0;\n\t\t\t\t\t\t\t\tborder-bottom: 1px solid #f0f0f0;\n\n\t\t\t\t\t\t\t\t.setting-label {\n\t\t\t\t\t\t\t\t\tfont-size: 28rpx;\n\t\t\t\t\t\t\t\t\tcolor: #333;\n\t\t\t\t\t\t\t\t}\n\n\t\t\t\t\t\t\t\t.copies-selector {\n\t\t\t\t\t\t\t\t\tdisplay: flex;\n\t\t\t\t\t\t\t\t\talign-items: center;\n\t\t\t\t\t\t\t\t\tbackground-color: #f5f5f5;\n\t\t\t\t\t\t\t\t\tborder-radius: 6rpx;\n\n\t\t\t\t\t\t\t\t\t.copies-btn {\n\t\t\t\t\t\t\t\t\t\twidth: 60rpx;\n\t\t\t\t\t\t\t\t\t\theight: 60rpx;\n\t\t\t\t\t\t\t\t\t\tdisplay: flex;\n\t\t\t\t\t\t\t\t\t\tjustify-content: center;\n\t\t\t\t\t\t\t\t\t\talign-items: center;\n\t\t\t\t\t\t\t\t\t\tfont-size: 32rpx;\n\t\t\t\t\t\t\t\t\t\tcolor: #333;\n\t\t\t\t\t\t\t\t\t\tbackground-color: #f0f0f0;\n\t\t\t\t\t\t\t\t\t}\n\n\t\t\t\t\t\t\t\t\t.copies-input {\n\t\t\t\t\t\t\t\t\t\twidth: 80rpx;\n\t\t\t\t\t\t\t\t\t\theight: 60rpx;\n\t\t\t\t\t\t\t\t\t\ttext-align: center;\n\t\t\t\t\t\t\t\t\t\tfont-size: 28rpx;\n\t\t\t\t\t\t\t\t\t\tcolor: #333;\n\t\t\t\t\t\t\t\t\t\tbackground-color: #ffffff;\n\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t}\n\n\t\t\t\t\t\t\t\t.option-selector {\n\t\t\t\t\t\t\t\t\tdisplay: flex;\n\t\t\t\t\t\t\t\t\tgap: 10rpx;\n\t\t\t\t\t\t\t\t}\n\n\t\t\t\t\t\t\t\t.option-btn {\n\t\t\t\t\t\t\t\t\twidth: 100rpx;\n\t\t\t\t\t\t\t\t\theight: 70rpx;\n\t\t\t\t\t\t\t\t\tdisplay: flex;\n\t\t\t\t\t\t\t\t\tjustify-content: center;\n\t\t\t\t\t\t\t\t\talign-items: center;\n\t\t\t\t\t\t\t\t\tbackground-color: #f5f5f5;\n\t\t\t\t\t\t\t\t\tcolor: #999;\n\t\t\t\t\t\t\t\t\tborder-radius: 6rpx;\n\t\t\t\t\t\t\t\t\tfont-size: 28rpx;\n\n\t\t\t\t\t\t\t\t\t&.active {\n\t\t\t\t\t\t\t\t\t\tbackground-color: #FFA500;\n\t\t\t\t\t\t\t\t\t\tcolor: #ffffff;\n\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t}\n\n\t\t\t\t\t\t\t.file-actions {\n\t\t\t\t\t\t\t\tdisplay: flex;\n\t\t\t\t\t\t\t\tjustify-content: flex-end;\n\t\t\t\t\t\t\t\tpadding-top: 20rpx;\n\n\t\t\t\t\t\t\t\t.action-btn {\n\t\t\t\t\t\t\t\t\tdisplay: flex;\n\t\t\t\t\t\t\t\t\tflex-direction: column;\n\t\t\t\t\t\t\t\t\talign-items: center;\n\t\t\t\t\t\t\t\t\tmargin-left: 40rpx;\n\n\t\t\t\t\t\t\t\t\ttext {\n\t\t\t\t\t\t\t\t\t\tfont-size: 24rpx;\n\t\t\t\t\t\t\t\t\t\tcolor: #333;\n\t\t\t\t\t\t\t\t\t\tmargin-top: 4rpx;\n\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\t}\n\n\t.fixed-bottom-area {\n\t\tposition: fixed;\n\t\tbottom: 0;\n\t\tleft: 0;\n\t\tright: 0;\n\t\twidth: 100%;\n\t\tbackground-color: #f8f8f8;\n\t\tbox-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.05);\n\t\tz-index: 10;\n\t\tpadding-bottom: env(safe-area-inset-bottom);\n\n\t\t.notice-text {\n\t\t\tfont-size: 24rpx;\n\t\t\tcolor: #ff5a5f;\n\t\t\ttext-align: center;\n\t\t\tpadding: 20rpx;\n\t\t\tline-height: 1.5;\n\t\t}\n\n\t\t.btn-group {\n\t\t\tdisplay: flex;\n\t\t\tjustify-content: space-between;\n\t\t\tpadding: 0 30rpx 30rpx;\n\n\t\t\t.select-btn {\n\t\t\t\tflex: 1;\n\t\t\t\theight: 80rpx;\n\t\t\t\tline-height: 80rpx;\n\t\t\t\tbackground-color: #ffffff;\n\t\t\t\tcolor: #FFA500;\n\t\t\t\tborder: 1px solid #FFA500;\n\t\t\t\tborder-radius: 40rpx;\n\t\t\t\tfont-size: 28rpx;\n\t\t\t\tmargin-right: 20rpx;\n\t\t\t}\n\n\t\t\t.print-btn {\n\t\t\t\tflex: 1;\n\t\t\t\theight: 80rpx;\n\t\t\t\tline-height: 80rpx;\n\t\t\t\tbackground-color: #CCCCCC;\n\t\t\t\tcolor: #ffffff;\n\t\t\t\tborder-radius: 40rpx;\n\t\t\t\tfont-size: 28rpx;\n\n\t\t\t\t&:not([disabled]) {\n\t\t\t\t\tbackground-color: #007aff;\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\t}\n}\n</style>\n", "import MiniProgramPage from 'D:/Yunchuang/PhotoFront/fotoBox-front/pages/photo/five-inch-photo.vue'\nwx.createPage(MiniProgramPage)"], "names": ["uni"], "mappings": ";;;AAqGA,MAAK,YAAU;AAAA,EACd,OAAO;AACN,WAAO;AAAA,MACN,WAAW,CAAC;AAAA,IACb;AAAA,EACA;AAAA,EAED,SAAS;AAAA;AAAA,IAER,WAAW;AACVA,oBAAAA,MAAI,gBAAgB;AAAA,QACnB,UAAU,CAAC,MAAM,OAAO;AAAA,QACxB,SAAS,CAAC,QAAQ;AACjB,cAAI,IAAI,aAAa,GAAG;AACvB,iBAAK,UAAS;AAAA,qBACJ,IAAI,aAAa,GAAG;AAC9B,iBAAK,qBAAoB;AAAA,UAC1B;AAAA,QACD;AAAA,MACD,CAAC;AAAA,IACD;AAAA;AAAA,IAGD,YAAY;AACXA,oBAAAA,MAAI,YAAY;AAAA,QACf,OAAO;AAAA,QACP,YAAY,CAAC,QAAQ;AAAA,QACrB,SAAS,CAAC,QAAQ;AACjB,eAAK,qBAAqB,IAAI,aAAa;AAAA,QAC3C;AAAA,QACD,MAAM,CAAC,QAAQ;AACdA,wBAAc,MAAA,MAAA,SAAA,0CAAA,SAAS,GAAG;AAC1BA,wBAAAA,MAAI,UAAU;AAAA,YACb,OAAO;AAAA,YACP,MAAM;AAAA,UACP,CAAC;AAAA,QACF;AAAA,MACD,CAAC;AAAA,IACD;AAAA;AAAA,IAGD,uBAAuB;AACtBA,oBAAAA,MAAI,YAAY;AAAA,QACf,OAAO;AAAA;AAAA,QACP,YAAY,CAAC,OAAO;AAAA,QACpB,SAAS,CAAC,QAAQ;AACjB,eAAK,qBAAqB,IAAI,aAAa;AAAA,QAC3C;AAAA,QACD,MAAM,CAAC,QAAQ;AACdA,uFAAc,WAAW,GAAG;AAC5BA,wBAAAA,MAAI,UAAU;AAAA,YACb,OAAO;AAAA,YACP,MAAM;AAAA,UACP,CAAC;AAAA,QACF;AAAA,MACD,CAAC;AAAA,IACD;AAAA;AAAA,IAGD,qBAAqB,WAAW;AAC/B,gBAAU,QAAQ,CAAC,MAAM,UAAU;AAClC,cAAM,QAAQ;AAAA,UACb;AAAA,UACA,MAAM,QAAQ,KAAK,UAAU,SAAS,QAAQ,CAAC;AAAA,UAC/C,QAAQ;AAAA,UACR,WAAW;AAAA,UACX,WAAW;AAAA,UACX,WAAW;AAAA,UACX,UAAU;AAAA;AAGX,aAAK,UAAU,KAAK,KAAK;AACzB,aAAK,eAAe,KAAK,UAAU,SAAS,CAAC;AAAA,MAC9C,CAAC;AAAA,IACD;AAAA;AAAA,IAGD,eAAe,OAAO;AACrB,YAAM,QAAQ,KAAK,UAAU,KAAK;AAClC,YAAM,WAAW,YAAY,MAAM;AAClC,cAAM,YAAY,KAAK,OAAM,IAAK;AAClC,YAAI,MAAM,YAAY,KAAK;AAC1B,gBAAM,WAAW;AACjB,gBAAM,YAAY;AAClB,wBAAc,QAAQ;AAAA,QACvB;AAAA,MACA,GAAE,GAAG;AAAA,IACN;AAAA;AAAA,IAGD,eAAe,OAAO;AACrB,UAAI,KAAK,UAAU,KAAK,EAAE,SAAS,IAAI;AACtC,aAAK,UAAU,KAAK,EAAE;AAAA,MACvB;AAAA,IACA;AAAA;AAAA,IAGD,eAAe,OAAO;AACrB,UAAI,KAAK,UAAU,KAAK,EAAE,SAAS,GAAG;AACrC,aAAK,UAAU,KAAK,EAAE;AAAA,MACvB;AAAA,IACA;AAAA;AAAA,IAGD,aAAa,OAAO,MAAM;AACzB,WAAK,UAAU,KAAK,EAAE,YAAY;AAAA,IAClC;AAAA;AAAA,IAGD,aAAa,OAAO,MAAM;AACzB,WAAK,UAAU,KAAK,EAAE,YAAY;AAAA,IAClC;AAAA;AAAA,IAGD,aAAa,OAAO;AACnBA,oBAAAA,MAAI,aAAa;AAAA,QAChB,MAAM,CAAC,MAAM,IAAI;AAAA,QACjB,SAAS,MAAM;AAAA,MAChB,CAAC;AAAA,IACD;AAAA;AAAA,IAGD,YAAY,OAAO;AAClBA,oBAAAA,MAAI,UAAU;AAAA,QACb,OAAO;AAAA,QACP,SAAS;AAAA,QACT,SAAS,CAAC,QAAQ;AACjB,cAAI,IAAI,SAAS;AAChB,iBAAK,UAAU,OAAO,OAAO,CAAC;AAAA,UAC/B;AAAA,QACD;AAAA,MACD,CAAC;AAAA,IACD;AAAA;AAAA,IAGD,UAAU;AACT,UAAI,KAAK,UAAU,WAAW,GAAG;AAChCA,sBAAAA,MAAI,UAAU;AAAA,UACb,OAAO;AAAA,UACP,MAAM;AAAA,QACP,CAAC;AACD;AAAA,MACD;AAGA,YAAM,kBAAkB,KAAK,UAAU,OAAO,WAAS,MAAM,SAAS;AACtE,UAAI,gBAAgB,SAAS,GAAG;AAC/BA,sBAAAA,MAAI,UAAU;AAAA,UACb,OAAO;AAAA,UACP,MAAM;AAAA,QACP,CAAC;AACD;AAAA,MACD;AAGA,YAAM,cAAc,KAAK,UAAU,OAAO,CAAC,KAAK,UAAU,MAAM,MAAM,QAAQ,CAAC;AAC/E,YAAM,eAAe;AACrB,YAAM,aAAa,cAAc;AAGjCA,oBAAAA,MAAI,UAAU;AAAA,QACb,OAAO;AAAA,QACP,SAAS,IAAI,KAAK,UAAU,MAAM,OAAO,WAAW,WAAW,WAAW,QAAQ,CAAC,CAAC;AAAA,QACpF,aAAa;AAAA,QACb,YAAY;AAAA,QACZ,SAAS,CAAC,QAAQ;AACjB,cAAI,IAAI,SAAS;AAEhBA,0BAAAA,MAAI,UAAU;AAAA,cACb,OAAO;AAAA,cACP,MAAM;AAAA,YACP,CAAC;AAGD,uBAAW,MAAM;AAChBA,4BAAG,MAAC,aAAY;AAAA,YAChB,GAAE,IAAI;AAAA,UACR;AAAA,QACD;AAAA,MACD,CAAC;AAAA,IACF;AAAA,EACD;AACD;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AC1RA,GAAG,WAAW,eAAe;"}