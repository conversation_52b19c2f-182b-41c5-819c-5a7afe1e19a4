{"version": 3, "file": "business-license-print.js", "sources": ["pages/copy/business-license-print.vue", "C:/Program Files/HBuilderX.4.66.**********/plugins/uniapp-cli-vite/uniPage:/cGFnZXMvY29weS9idXNpbmVzcy1saWNlbnNlLXByaW50LnZ1ZQ"], "sourcesContent": ["<template>\n    <f-page-layout \n        title=\"营业执照拍照打印\" \n        fallback-path=\"/pages/copy/index\"\n        @back-click=\"onBackClick\"\n    >\n        <view class=\"business-license-print\">\n            <!-- 顶部提示 -->\n            <view class=\"business-license-print__header\">\n                <text class=\"business-license-print__tip\">内容拍照时需要与背景颜色尽量区分</text>\n            </view>\n\n            <!-- 营业执照表格区域 -->\n            <view class=\"business-license-print__license-container\" @click=\"showActionSheet\">\n                <!-- 页面标题 -->\n                <view class=\"business-license-print__title\">\n                    <text class=\"business-license-print__title-text\">营业执照拍照打印</text>\n                </view>\n\n                <!-- 表格行 -->\n                <view class=\"business-license-print__table\">\n                    <view class=\"business-license-print__row\" v-for=\"(row, index) in tableRows\" :key=\"index\">\n                        <view class=\"business-license-print__cell\" v-for=\"(cell, cellIndex) in row\" :key=\"cellIndex\">\n                            <text class=\"business-license-print__cell-text\">{{ cell }}</text>\n                        </view>\n                    </view>\n                </view>\n\n                <!-- 中央拍照区域 -->\n                <view class=\"business-license-print__photo-area\">\n                    <view class=\"business-license-print__camera-circle\">\n                        <uni-icons type=\"camera\" color=\"#FFFFFF\" size=\"40\"></uni-icons>\n                    </view>\n                    <text class=\"business-license-print__photo-text\">拍摄营业执照</text>\n                </view>\n            </view>\n        </view>\n    </f-page-layout>\n</template>\n\n<script>\nexport default {\n    data() {\n        return {\n            businessLicensePhoto: '', // 用户拍摄的营业执照照片\n            // 营业执照模板图片 - 留空供后续引用\n            businessLicenseTemplateImage: '', // 营业执照模板图片路径\n            // 表格行数据（模拟营业执照表格结构）\n            tableRows: [\n                ['', '', '', ''],\n                ['', '', '', ''],\n                ['', '', '', ''],\n                ['', '', '', ''],\n                ['', '', '', ''],\n                ['', '', '', ''],\n                ['', '', '', ''],\n                ['', '', '', ''],\n                ['', '', '', ''],\n                ['', '', '', ''],\n                ['', '', '', ''],\n                ['', '', '', '']\n            ]\n        };\n    },\n    methods: {\n        // 返回上一页\n        onBackClick() {\n            uni.navigateBack();\n        },\n        \n        // 显示操作选择\n        showActionSheet() {\n            uni.showActionSheet({\n                itemList: ['拍照', '从相册选择'],\n                success: (res) => {\n                    if (res.tapIndex === 0) {\n                        this.takePhoto();\n                    } else if (res.tapIndex === 1) {\n                        this.chooseFromAlbum();\n                    }\n                }\n            });\n        },\n\n        // 拍照功能\n        takePhoto() {\n            uni.chooseImage({\n                count: 1,\n                sourceType: ['camera'],\n                success: (res) => {\n                    this.handleImageSelected(res.tempFilePaths[0]);\n                },\n                fail: (err) => {\n                    console.error('拍照失败:', err);\n                    uni.showToast({\n                        title: '拍照失败',\n                        icon: 'none'\n                    });\n                }\n            });\n        },\n\n        // 从相册选择\n        chooseFromAlbum() {\n            uni.chooseImage({\n                count: 1,\n                sourceType: ['album'],\n                success: (res) => {\n                    this.handleImageSelected(res.tempFilePaths[0]);\n                },\n                fail: (err) => {\n                    console.error('选择图片失败:', err);\n                    uni.showToast({\n                        title: '选择图片失败',\n                        icon: 'none'\n                    });\n                }\n            });\n        },\n\n        // 处理选择的图片\n        handleImageSelected(imagePath) {\n            this.businessLicensePhoto = imagePath;\n            uni.showToast({\n                title: '营业执照拍摄完成',\n                icon: 'success'\n            });\n\n            // 自动提示进入打印设置\n            setTimeout(() => {\n                this.proceedToPrintSettings();\n            }, 1500);\n        },\n        \n        // 跳转到打印设置\n        proceedToPrintSettings() {\n            uni.showModal({\n                title: '拍摄完成',\n                content: '营业执照已拍摄完成，是否进入打印设置？',\n                success: (res) => {\n                    if (res.confirm) {\n                        this.confirmPrint();\n                    }\n                }\n            });\n        },\n\n        // 确认打印\n        confirmPrint() {\n            if (!this.businessLicensePhoto) {\n                uni.showToast({\n                    title: '请先拍摄营业执照',\n                    icon: 'none'\n                });\n                return;\n            }\n\n            // 跳转到打印设置页面，传递照片数据\n            const photoData = encodeURIComponent(JSON.stringify({\n                businessLicense: this.businessLicensePhoto\n            }));\n            uni.navigateTo({\n\n            });\n        },\n\n        // 设置营业执照模板图片 - 供后续引用\n        setBusinessLicenseTemplateImage(templateImageUrl) {\n            this.businessLicenseTemplateImage = templateImageUrl;\n        }\n    },\n\n    // 页面加载时的处理\n    onLoad() {\n        // 这里可以设置默认的营业执照模板图片\n        // 示例：this.setBusinessLicenseTemplateImage('/static/images/business-license-template.png');\n    }\n};\n</script>\n\n<style lang=\"scss\">\n.business-license-print {\n    background-color: #f5f5f5;\n    min-height: 100vh;\n\n    &__header {\n        background: linear-gradient(135deg, #FFE4B5 0%, #FFF8DC 100%);\n        padding: 20rpx;\n        text-align: center;\n        margin: 0;\n    }\n\n    &__tip {\n        font-size: 28rpx;\n        color: #D2691E;\n        font-weight: 500;\n    }\n\n    &__license-container {\n        background-color: #F5E6A8;\n        margin: 20rpx;\n        border-radius: 16rpx;\n        padding: 40rpx 30rpx;\n        position: relative;\n        min-height: 800rpx;\n        box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);\n\n        &:active {\n            opacity: 0.9;\n        }\n    }\n\n    &__title {\n        text-align: center;\n        margin-bottom: 50rpx;\n    }\n\n    &__title-text {\n        font-size: 48rpx;\n        font-weight: bold;\n        color: #FFA500;\n        text-shadow: 2rpx 2rpx 4rpx rgba(0, 0, 0, 0.1);\n    }\n\n    &__table {\n        margin-bottom: 40rpx;\n        width: 100%;\n    }\n\n    &__row {\n        display: flex;\n        margin-bottom: 12rpx;\n\n        &:last-child {\n            margin-bottom: 0;\n        }\n    }\n\n    &__cell {\n        flex: 1;\n        height: 60rpx;\n        background-color: #FFF8DC;\n        border: 2rpx solid #E6D35A;\n        margin-right: 12rpx;\n        border-radius: 6rpx;\n        display: flex;\n        align-items: center;\n        justify-content: center;\n\n        &:last-child {\n            margin-right: 0;\n        }\n    }\n\n    &__cell-text {\n        font-size: 24rpx;\n        color: transparent; // 隐藏文字，只显示表格结构\n    }\n\n    &__photo-area {\n        position: absolute;\n        top: 50%;\n        left: 50%;\n        transform: translate(-50%, -50%);\n        display: flex;\n        flex-direction: column;\n        align-items: center;\n        gap: 20rpx;\n        z-index: 10;\n    }\n\n    &__camera-circle {\n        width: 140rpx;\n        height: 140rpx;\n        background: linear-gradient(135deg, rgba(0, 0, 0, 0.8) 0%, rgba(60, 60, 60, 0.8) 100%);\n        border-radius: 50%;\n        display: flex;\n        align-items: center;\n        justify-content: center;\n        box-shadow: 0 6rpx 20rpx rgba(0, 0, 0, 0.4);\n        border: 4rpx solid rgba(255, 255, 255, 0.3);\n\n        &:active {\n            transform: scale(0.95);\n            transition: transform 0.1s ease;\n        }\n    }\n\n    &__photo-text {\n        font-size: 32rpx;\n        color: #333333;\n        font-weight: bold;\n        text-shadow: 0 2rpx 4rpx rgba(255, 255, 255, 0.8);\n        background: rgba(255, 255, 255, 0.9);\n        padding: 8rpx 16rpx;\n        border-radius: 20rpx;\n        border: 2rpx solid #E6D35A;\n    }\n}\n\n/* 适配不同屏幕尺寸 */\n@media screen and (max-width: 375px) {\n    .business-license-print {\n        &__license-container {\n            margin: 15rpx;\n            padding: 30rpx 20rpx;\n            min-height: 700rpx;\n        }\n\n        &__title-text {\n            font-size: 40rpx;\n        }\n\n        &__camera-circle {\n            width: 100rpx;\n            height: 100rpx;\n        }\n\n        &__photo-text {\n            font-size: 28rpx;\n        }\n\n        &__cell {\n            height: 45rpx;\n        }\n    }\n}\n</style>\n", "import MiniProgramPage from 'D:/Yunchuang/PhotoFront/fotoBox-front/pages/copy/business-license-print.vue'\nwx.createPage(MiniProgramPage)"], "names": ["uni"], "mappings": ";;AAyCA,MAAK,YAAU;AAAA,EACX,OAAO;AACH,WAAO;AAAA,MACH,sBAAsB;AAAA;AAAA;AAAA,MAEtB,8BAA8B;AAAA;AAAA;AAAA,MAE9B,WAAW;AAAA,QACP,CAAC,IAAI,IAAI,IAAI,EAAE;AAAA,QACf,CAAC,IAAI,IAAI,IAAI,EAAE;AAAA,QACf,CAAC,IAAI,IAAI,IAAI,EAAE;AAAA,QACf,CAAC,IAAI,IAAI,IAAI,EAAE;AAAA,QACf,CAAC,IAAI,IAAI,IAAI,EAAE;AAAA,QACf,CAAC,IAAI,IAAI,IAAI,EAAE;AAAA,QACf,CAAC,IAAI,IAAI,IAAI,EAAE;AAAA,QACf,CAAC,IAAI,IAAI,IAAI,EAAE;AAAA,QACf,CAAC,IAAI,IAAI,IAAI,EAAE;AAAA,QACf,CAAC,IAAI,IAAI,IAAI,EAAE;AAAA,QACf,CAAC,IAAI,IAAI,IAAI,EAAE;AAAA,QACf,CAAC,IAAI,IAAI,IAAI,EAAE;AAAA,MACnB;AAAA;EAEP;AAAA,EACD,SAAS;AAAA;AAAA,IAEL,cAAc;AACVA,oBAAG,MAAC,aAAY;AAAA,IACnB;AAAA;AAAA,IAGD,kBAAkB;AACdA,oBAAAA,MAAI,gBAAgB;AAAA,QAChB,UAAU,CAAC,MAAM,OAAO;AAAA,QACxB,SAAS,CAAC,QAAQ;AACd,cAAI,IAAI,aAAa,GAAG;AACpB,iBAAK,UAAS;AAAA,qBACP,IAAI,aAAa,GAAG;AAC3B,iBAAK,gBAAe;AAAA,UACxB;AAAA,QACJ;AAAA,MACJ,CAAC;AAAA,IACJ;AAAA;AAAA,IAGD,YAAY;AACRA,oBAAAA,MAAI,YAAY;AAAA,QACZ,OAAO;AAAA,QACP,YAAY,CAAC,QAAQ;AAAA,QACrB,SAAS,CAAC,QAAQ;AACd,eAAK,oBAAoB,IAAI,cAAc,CAAC,CAAC;AAAA,QAChD;AAAA,QACD,MAAM,CAAC,QAAQ;AACXA,wBAAA,MAAA,MAAA,SAAA,+CAAc,SAAS,GAAG;AAC1BA,wBAAAA,MAAI,UAAU;AAAA,YACV,OAAO;AAAA,YACP,MAAM;AAAA,UACV,CAAC;AAAA,QACL;AAAA,MACJ,CAAC;AAAA,IACJ;AAAA;AAAA,IAGD,kBAAkB;AACdA,oBAAAA,MAAI,YAAY;AAAA,QACZ,OAAO;AAAA,QACP,YAAY,CAAC,OAAO;AAAA,QACpB,SAAS,CAAC,QAAQ;AACd,eAAK,oBAAoB,IAAI,cAAc,CAAC,CAAC;AAAA,QAChD;AAAA,QACD,MAAM,CAAC,QAAQ;AACXA,6FAAc,WAAW,GAAG;AAC5BA,wBAAAA,MAAI,UAAU;AAAA,YACV,OAAO;AAAA,YACP,MAAM;AAAA,UACV,CAAC;AAAA,QACL;AAAA,MACJ,CAAC;AAAA,IACJ;AAAA;AAAA,IAGD,oBAAoB,WAAW;AAC3B,WAAK,uBAAuB;AAC5BA,oBAAAA,MAAI,UAAU;AAAA,QACV,OAAO;AAAA,QACP,MAAM;AAAA,MACV,CAAC;AAGD,iBAAW,MAAM;AACb,aAAK,uBAAsB;AAAA,MAC9B,GAAE,IAAI;AAAA,IACV;AAAA;AAAA,IAGD,yBAAyB;AACrBA,oBAAAA,MAAI,UAAU;AAAA,QACV,OAAO;AAAA,QACP,SAAS;AAAA,QACT,SAAS,CAAC,QAAQ;AACd,cAAI,IAAI,SAAS;AACb,iBAAK,aAAY;AAAA,UACrB;AAAA,QACJ;AAAA,MACJ,CAAC;AAAA,IACJ;AAAA;AAAA,IAGD,eAAe;AACX,UAAI,CAAC,KAAK,sBAAsB;AAC5BA,sBAAAA,MAAI,UAAU;AAAA,UACV,OAAO;AAAA,UACP,MAAM;AAAA,QACV,CAAC;AACD;AAAA,MACJ;AAGkB,yBAAmB,KAAK,UAAU;AAAA,QAChD,iBAAiB,KAAK;AAAA,MAC1B,CAAC,CAAC;AACFA,oBAAAA,MAAI,WAAW,CAEf,CAAC;AAAA,IACJ;AAAA;AAAA,IAGD,gCAAgC,kBAAkB;AAC9C,WAAK,+BAA+B;AAAA,IACxC;AAAA,EACH;AAAA;AAAA,EAGD,SAAS;AAAA,EAGT;AACJ;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AChLA,GAAG,WAAW,eAAe;"}