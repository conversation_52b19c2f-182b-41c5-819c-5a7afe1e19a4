/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.bank-card-print {
  padding: 20rpx;
}
.bank-card-print__header {
  background: linear-gradient(135deg, #FFE4B5 0%, #FFF8DC 100%);
  padding: 20rpx;
  border-radius: 12rpx;
  margin-bottom: 30rpx;
  text-align: center;
}
.bank-card-print__tip {
  font-size: 28rpx;
  color: #D2691E;
  font-weight: 500;
}
.bank-card-print__content {
  display: flex;
  flex-direction: column;
  gap: 30rpx;
}
.bank-card-print__card-container {
  position: relative;
  border-radius: 20rpx;
  overflow: hidden;
  box-shadow: 0 8rpx 30rpx rgba(0, 0, 0, 0.1);
}
.bank-card-print__card-container:active {
  transform: scale(0.98);
  transition: transform 0.1s ease;
}
.bank-card-print__card {
  position: relative;
  width: 100%;
  height: 400rpx;
  border-radius: 20rpx;
  overflow: hidden;
}
.bank-card-print__card-image {
  width: 100%;
  height: 100%;
  position: absolute;
  top: 0;
  left: 0;
  border-radius: 20rpx;
  object-fit: cover;
}
.bank-card-print__card-placeholder {
  width: 100%;
  height: 100%;
  position: absolute;
  top: 0;
  left: 0;
  border-radius: 20rpx;
  background: linear-gradient(135deg, #F5F5F5 0%, #E8E8E8 100%);
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  border: 2rpx dashed #CCCCCC;
  box-sizing: border-box;
}
.bank-card-print__placeholder-text {
  font-size: 28rpx;
  color: #999999;
  font-weight: 500;
  margin-bottom: 8rpx;
}
.bank-card-print__placeholder-desc {
  font-size: 24rpx;
  color: #BBBBBB;
}
.bank-card-print__camera-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  border-radius: 20rpx;
}
.bank-card-print__camera-icon {
  width: 120rpx;
  height: 120rpx;
  background: rgba(0, 0, 0, 0.6);
  border-radius: 50%;
  display: flex;
  justify-content: center;
  align-items: center;
  margin-bottom: 20rpx;
}
.bank-card-print__camera-text {
  font-size: 32rpx;
  color: #fff;
  font-weight: 500;
  text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.5);
}

/* 适配不同屏幕尺寸 */
@media screen and (max-width: 375px) {
.bank-card-print {
    padding: 15rpx;
}
.bank-card-print__card {
    height: 350rpx;
}
.bank-card-print__card-bg {
    padding: 25rpx;
}
.bank-card-print__bank-logo {
    font-size: 28rpx;
}
.bank-card-print__bank-name {
    font-size: 24rpx;
}
.bank-card-print__number-group {
    font-size: 28rpx;
}
}