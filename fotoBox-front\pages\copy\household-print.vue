<template>
    <f-page-layout 
        title="户口本打印" 
        fallback-path="/pages/copy/index"
        @back-click="onBackClick"
    >
        <view class="household-print">
            <!-- 顶部提示 -->
            <view class="tip-banner">
                <text class="tip-text">内容占照时需要与背景颜色尽量区分</text>
            </view>
            
            <!-- 户主页 -->
            <view class="page-card" @click="showActionSheet('master')">
                <view class="page-header">
                    <text class="page-title">户主页</text>
                </view>

                <view class="page-content">
                    <!-- 户口本户主页背景图片 -->
                    <image
                        v-if="masterPageImage"
                        class="household-print__page-image"
                        :src="masterPageImage"
                        mode="aspectFill"
                    />
                    <!-- 无图片时的占位符 -->
                    <view v-else class="household-print__page-placeholder">
                        <view class="household-print__placeholder-text">户主页模板</view>
                        <view class="household-print__placeholder-desc">图片将在此处显示</view>
                    </view>

                    <!-- 相机图标覆盖层 -->
                    <view class="household-print__camera-overlay">
                        <view class="household-print__camera">
                            <uni-icons type="camera" color="#FFFFFF" size="30"></uni-icons>
                        </view>
                        <view class="household-print__text">
                            拍摄户主页
                        </view>
                    </view>
                </view>
            </view>
            
            <!-- 个人信息页 -->
            <view class="page-card" @click="showActionSheet('personal')">
                <view class="page-header">
                    <text class="page-title">个人信息页</text>
                </view>

                <view class="page-content">
                    <!-- 户口本个人信息页背景图片 -->
                    <image
                        v-if="personalPageImage"
                        class="household-print__page-image"
                        :src="personalPageImage"
                        mode="aspectFill"
                    />
                    <!-- 无图片时的占位符 -->
                    <view v-else class="household-print__page-placeholder">
                        <view class="household-print__placeholder-text">个人信息页模板</view>
                        <view class="household-print__placeholder-desc">图片将在此处显示</view>
                    </view>

                    <!-- 相机图标覆盖层 -->
                    <view class="household-print__camera-overlay">
                        <view class="household-print__camera">
                            <uni-icons type="camera" color="#FFFFFF" size="30"></uni-icons>
                        </view>
                        <view class="household-print__text">
                            拍摄个人信息页
                        </view>
                    </view>
                </view>
            </view>
            
            <!-- 底部操作按钮 -->
            <view class="action-buttons">
                <button class="btn btn-secondary" @click="resetPhotos">重新拍照</button>
                <button class="btn btn-primary" @click="confirmPrint" :disabled="!canPrint">确认打印</button>
            </view>
        </view>
    </f-page-layout>
</template>

<script>
export default {
    data() {
        return {
            currentPage: '', // 当前拍摄的页面 'master' 或 'personal'
            photos: {
                master: '', // 户主页拍摄照片
                personal: '' // 个人信息页拍摄照片
            },
            // 户口本模板图片 - 留空供后续引用
            masterPageImage: '', // 户主页模板图片路径
            personalPageImage: '' // 个人信息页模板图片路径
        };
    },
    computed: {
        // 检查是否可以打印（两张照片都已拍摄）
        canPrint() {
            return this.photos.master && this.photos.personal;
        }
    },
    methods: {
        // 返回上一页
        onBackClick() {
            uni.navigateBack();
        },
        
        // 显示操作选择
        showActionSheet(page) {
            this.currentPage = page;
            const pageText = page === 'master' ? '户主页' : '个人信息页';

            uni.showActionSheet({
                itemList: ['拍照', '从相册选择'],
                success: (res) => {
                    if (res.tapIndex === 0) {
                        this.takePhoto();
                    } else if (res.tapIndex === 1) {
                        this.chooseFromAlbum();
                    }
                }
            });
        },

        // 拍照
        takePhoto() {
            uni.chooseImage({
                count: 1,
                sourceType: ['camera'],
                success: (res) => {
                    this.handleImageSelected(res.tempFilePaths[0]);
                },
                fail: (err) => {
                    console.error('拍照失败:', err);
                    uni.showToast({
                        title: '拍照失败',
                        icon: 'none'
                    });
                }
            });
        },

        // 从相册选择
        chooseFromAlbum() {
            uni.chooseImage({
                count: 1,
                sourceType: ['album'],
                success: (res) => {
                    this.handleImageSelected(res.tempFilePaths[0]);
                },
                fail: (err) => {
                    console.error('选择图片失败:', err);
                    uni.showToast({
                        title: '选择图片失败',
                        icon: 'none'
                    });
                }
            });
        },

        // 处理选择的图片
        handleImageSelected(imagePath) {
            this.photos[this.currentPage] = imagePath;

            const pageText = this.currentPage === 'master' ? '户主页' : '个人信息页';
            uni.showToast({
                title: `${pageText}拍摄完成`,
                icon: 'success'
            });

            // 检查是否两页都已拍摄
            if (this.canPrint) {
                setTimeout(() => {
                    this.proceedToPrintSettings();
                }, 1500);
            }
        },
        
        // 重新拍照
        resetPhotos() {
            uni.showModal({
                title: '确认重新拍照',
                content: '是否要清除所有已拍摄的照片？',
                success: (res) => {
                    if (res.confirm) {
                        this.photos = {
                            master: '',
                            personal: ''
                        };
                        uni.showToast({
                            title: '已清除照片',
                            icon: 'success'
                        });
                    }
                }
            });
        },
        
        // 跳转到打印设置
        proceedToPrintSettings() {
            uni.showModal({
                title: '拍摄完成',
                content: '户口本两页已拍摄完成，是否进入打印设置？',
                success: (res) => {
                    if (res.confirm) {
                        this.confirmPrint();
                    }
                }
            });
        },

        // 确认打印
        confirmPrint() {
            if (!this.canPrint) {
                uni.showToast({
                    title: '请先拍摄户主页和个人信息页',
                    icon: 'none'
                });
                return;
            }

            // 跳转到打印设置页面，传递照片数据
            const photosData = encodeURIComponent(JSON.stringify(this.photos));
            uni.navigateTo({

            });
        },

        // 设置户口本模板图片 - 供后续引用
        setHouseholdTemplateImages(masterImageUrl, personalImageUrl) {
            this.masterPageImage = masterImageUrl;
            this.personalPageImage = personalImageUrl;
        }
    },

    // 页面加载时的处理
    onLoad() {
        // 这里可以设置默认的户口本模板图片
        // 示例：this.setHouseholdTemplateImages('/static/images/household-master.png', '/static/images/household-personal.png');
    }
};
</script>

<style lang="scss">
.household-print {
    background-color: #f5f5f5;
    min-height: 100vh;
    padding-bottom: 120rpx; // 为底部按钮留出空间
}

.tip-banner {
    background: linear-gradient(135deg, #FFE082 0%, #FFA726 100%);
    padding: 20rpx;
    margin: 20rpx;
    border-radius: 12rpx;
    text-align: center;

    .tip-text {
        color: #8D6E63;
        font-size: 26rpx;
        font-weight: 500;
    }
}

.page-card {
    background-color: #fff;
    margin: 20rpx;
    border-radius: 16rpx;
    overflow: hidden;
    box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);

    .page-header {
        background-color: #f8f9fa;
        padding: 30rpx;
        border-bottom: 2rpx solid #e9ecef;

        .page-title {
            font-size: 32rpx;
            font-weight: bold;
            color: #333;
        }
    }

    .page-content {
        position: relative;
        height: 500rpx; // 固定高度，模拟户口本页面比例
        overflow: hidden;

        &:active {
            opacity: 0.9;
        }
    }
}

// 户口本页面图片样式
.household-print {
    &__page-image {
        width: 100%;
        height: 100%;
        position: absolute;
        top: 0;
        left: 0;
        object-fit: cover;
    }

    &__page-placeholder {
        width: 100%;
        height: 100%;
        position: absolute;
        top: 0;
        left: 0;
        background: linear-gradient(135deg, #F5F5F5 0%, #E8E8E8 100%);
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        border: 2rpx dashed #CCCCCC;
        box-sizing: border-box;
    }

    &__placeholder-text {
        font-size: 28rpx;
        color: #999999;
        font-weight: 500;
        margin-bottom: 8rpx;
    }

    &__placeholder-desc {
        font-size: 24rpx;
        color: #BBBBBB;
    }

    // 相机覆盖层样式
    &__camera-overlay {
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: rgba(0, 0, 0, 0.4);
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        gap: 20rpx;
    }

    &__camera {
        width: 120rpx;
        height: 120rpx;
        background-color: rgba(0, 0, 0, 0.6);
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
    }

    &__text {
        font-size: 32rpx;
        color: #FFFFFF;
        font-weight: 500;
        text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.5);
        text-align: center;
    }
}







.action-buttons {
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    background-color: #fff;
    padding: 30rpx;
    border-top: 1rpx solid #e9ecef;
    display: flex;
    gap: 20rpx;

    .btn {
        flex: 1;
        height: 88rpx;
        border-radius: 44rpx;
        font-size: 32rpx;
        font-weight: 500;
        border: none;

        &-secondary {
            background-color: #f8f9fa;
            color: #6c757d;
        }

        &-primary {
            background-color: #FFA500;
            color: #fff;

            &:disabled {
                background-color: #ccc;
                color: #999;
            }
        }
    }
}

/* 适配不同屏幕尺寸 */
@media screen and (max-width: 375px) {
    .page-card {
        margin: 15rpx;

        .page-content {
            height: 450rpx;
        }
    }

    .household-print {
        &__camera {
            width: 100rpx;
            height: 100rpx;
        }

        &__text {
            font-size: 28rpx;
        }

        &__placeholder-text {
            font-size: 24rpx;
        }

        &__placeholder-desc {
            font-size: 20rpx;
        }
    }

    .action-buttons {
        padding: 20rpx;

        .btn {
            height: 80rpx;
            font-size: 28rpx;
        }
    }
}
</style>
