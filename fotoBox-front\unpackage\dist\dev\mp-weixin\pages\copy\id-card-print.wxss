/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.id-card-print {
  background-color: #FFF8E1;
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}
.id-card-print__header {
  background-color: #FFD180;
  padding: 16rpx;
  text-align: center;
}
.id-card-print__tip {
  font-size: 26rpx;
  color: #795548;
}
.id-card-print__content {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 30rpx 20rpx;
}
.id-card-print__title {
  font-size: 46rpx;
  font-weight: bold;
  color: #FFA000;
  margin-bottom: 50rpx;
  text-align: center;
}
.id-card-print__preview {
  width: 100%;
  display: flex;
  justify-content: center;
  padding: 0 30rpx;
  margin-bottom: 40rpx;
}
.id-card-print__preview:last-child {
  margin-bottom: 0;
}
.id-card-print__card {
  width: 100%;
  max-width: 550rpx;
  aspect-ratio: 1.586;
  background-color: #fff;
  border-radius: 12rpx;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.08);
  display: flex;
  flex-direction: column;
  position: relative;
  overflow: hidden;
}
.id-card-print__card-image {
  width: 100%;
  height: 100%;
  position: absolute;
  top: 0;
  left: 0;
  border-radius: 12rpx;
  object-fit: cover;
}
.id-card-print__card-placeholder {
  width: 100%;
  height: 100%;
  position: absolute;
  top: 0;
  left: 0;
  border-radius: 12rpx;
  background: linear-gradient(135deg, #F5F5F5 0%, #E8E8E8 100%);
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  border: 2rpx dashed #CCCCCC;
  box-sizing: border-box;
}
.id-card-print__placeholder-text {
  font-size: 28rpx;
  color: #999999;
  font-weight: 500;
  margin-bottom: 8rpx;
}
.id-card-print__placeholder-desc {
  font-size: 24rpx;
  color: #BBBBBB;
}
.id-card-print__camera-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.4);
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  gap: 20rpx;
  border-radius: 12rpx;
}
.id-card-print__camera {
  width: 120rpx;
  height: 120rpx;
  background-color: rgba(0, 0, 0, 0.6);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
}
.id-card-print__text {
  font-size: 32rpx;
  color: #FFFFFF;
  font-weight: 500;
  text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.5);
  text-align: center;
}

/* 适配不同屏幕尺寸 */
@media screen and (max-width: 375px) {
.id-card-print__card {
    max-width: 500rpx;
}
.id-card-print__camera {
    width: 100rpx;
    height: 100rpx;
}
.id-card-print__text {
    font-size: 28rpx;
}
.id-card-print__placeholder-text {
    font-size: 24rpx;
}
.id-card-print__placeholder-desc {
    font-size: 20rpx;
}
}