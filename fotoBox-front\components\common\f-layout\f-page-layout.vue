<template>
	<view class="f-page-layout">
		<!-- 页面头部 -->
		<f-page-header 
			:title="title" 
			:bg-color="headerBgColor"
			:fallback-path="fallbackPath"
			:custom-back-path="customBackPath"
			:delta="delta"
			@back-click="onBackClick" 
		/>
		
		<!-- 页面内容区域 -->
		<view class="f-page-layout__content">
			<slot></slot>
		</view>
	</view>
</template>

<script>
	import FPageHeader from '@/components/common/f-navbar/f-page-header.vue';
	
	export default {
		name: 'FPageLayout',
		components: {
			FPageHeader
		},
		props: {
			// 页面标题
			title: {
				type: String,
				default: '页面标题'
			},
			// 头部背景色
			headerBgColor: {
				type: String,
				default: '#FFA500' // 橙色背景
			},
			// 自定义返回路径，如果提供，则优先使用
			customBackPath: {
				type: String,
				default: ''
			},
			// 备用路径，当返回失败时使用
			fallbackPath: {
				type: String,
				default: '/pages/index/index'
			},
			// 返回时要返回的层级数
			delta: {
				type: Number,
				default: 1
			}
		},
		methods: {
			// 返回上一页
			onBackClick() {
				this.$emit('back-click');
			}
		}
	}
</script>

<style lang="scss">
	.f-page-layout {
		display: flex;
		flex-direction: column;
		min-height: 100vh;
		background-color: #f5f5f5;
		
		&__content {
			flex: 1;
			display: flex;
			flex-direction: column;
		}
	}
</style> 