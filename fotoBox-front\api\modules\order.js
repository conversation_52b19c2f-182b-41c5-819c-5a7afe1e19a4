/**
 * 订单相关API
 */
import request from '../request';
import { baseURL } from '@/config/env';

/**
 * 创建打印订单
 * @param {Object} data - 订单参数
 * @param {String} data.deviceId - 打印机设备ID
 * @param {String} data.deviceName - 打印机设备名称（可选）
 * @param {String} data.phone - 用户手机号（可选）
 */
export function createPrintOrder(data) {
  // 调试：检查传入的参数
  console.log('createPrintOrder 参数:', data);

  // 确保deviceId存在
  if (!data.deviceId) {
    return Promise.reject({ code: 400, msg: 'deviceId 参数不能为空' });
  }

  return request({
    url: '/order/printer/createOrder',
    method: 'POST',
    data: data,
    header: {
      'Content-Type': 'application/x-www-form-urlencoded'  // 改为表单格式
    }
  });
}

/**
 * 上传打印任务
 * @param {Object} data - 上传参数
 * @param {String} data.orderId - 订单ID
 * @param {String} data.filePath - 文件路径
 * @param {Number} data.copies - 打印份数，默认1
 * @param {Number} data.colorMode - 颜色模式：0-黑白，1-彩色，默认0
 * @param {Number} data.duplexMode - 双面模式：0-单面，1-双面，默认0
 * @param {Number} data.paperType - 纸张类型：1-A4，2-A5，3-照片纸，默认1
 * @param {String} data.pageRange - 页码范围
 * @param {Function} data.onProgress - 上传进度回调
 */
export function uploadFileAndCalculatePrice(data) {
  const token = uni.getStorageSync('token');

  if (!token) {
    uni.showToast({
      title: '请先登录',
      icon: 'none'
    });
    setTimeout(() => {
      uni.navigateTo({
        url: '/pages/login/index'
      });
    }, 1500);
    return Promise.reject({ code: 401, msg: '请先登录' });
  }

  return new Promise((resolve, reject) => {
    const uploadTask = uni.uploadFile({
      url: baseURL + '/order/printer/uploadFile',
      filePath: data.filePath,
      name: 'file',
      formData: {
        orderId: data.orderId,
        copies: data.copies || 1,
        colorMode: data.colorMode || 0,
        duplexMode: data.duplexMode || 0,
        paperType: data.paperType || 1,
        pageRange: data.pageRange || 'all'
      },
      header: {
        'Authorization': token // 直接使用token，不加Bearer前缀
      },
      success: (res) => {
        if (res.statusCode === 200) {
          try {
            const responseData = typeof res.data === 'string' ? JSON.parse(res.data) : res.data;
            
            if (responseData.code === 401) {
              uni.removeStorageSync('token');
              uni.removeStorageSync('userInfo');
              uni.showToast({
                title: '登录已过期，请重新登录',
                icon: 'none'
              });
              setTimeout(() => {
                uni.navigateTo({
                  url: '/pages/login/index'
                });
              }, 1500);
              reject(responseData);
              return;
            }
            
            resolve(responseData);
          } catch (error) {
            reject(new Error('解析响应数据失败'));
          }
        } else {
          reject(res);
        }
      },
      fail: (err) => {
        reject(err);
      }
    });

    if (data.onProgress) {
      uploadTask.onProgressUpdate((res) => {
        data.onProgress(res.progress);
      });
    }
  });
}

/**
 * 批量上传打印任务
 * @param {Object} data - 批量上传参数
 * @param {String} data.orderId - 订单ID
 * @param {Array} data.fileList - 文件列表
 * @param {Function} data.onProgress - 上传进度回调
 * @param {Function} data.onFileComplete - 单个文件完成回调
 */
export function batchUploadFiles(data) {
  const uploadPromises = data.fileList.map((file, index) => {
    return uploadFileAndCalculatePrice({
      orderId: data.orderId,
      filePath: file.path,
      copies: file.copies || 1,
      colorMode: file.printType === 'color' ? 1 : 0,
      duplexMode: file.printMode === 'double' ? 1 : 0,
      paperType: file.paperType || 1,
      pageRange: file.pageRange || `${file.startPage || 1}-${file.endPage || 1}`,
      onProgress: (progress) => {
        if (data.onProgress) {
          data.onProgress(index, progress);
        }
      }
    }).then(result => {
      if (data.onFileComplete) {
        data.onFileComplete(index, result);
      }
      return result;
    });
  });

  return Promise.all(uploadPromises);
}

/**
 * 获取订单详情
 * @param {String} orderId - 订单ID
 */
export function getOrderDetail(orderId) {
  return request({
    url: `/order/printer/detail/${orderId}`,
    method: 'GET'
  });
}

/**
 * 取消订单
 * @param {String} orderId - 订单ID
 */
export function cancelOrder(orderId) {
  return request({
    url: `/order/printer/${orderId}/cancel`,
    method: 'POST'
  });
}

/**
 * 获取订单列表
 * @param {Object} params - 查询参数
 * @param {Number} params.page - 页码，默认1
 * @param {Number} params.size - 每页数量，默认10
 * @param {String} params.status - 订单状态筛选
 */
export function getOrderList(params = {}) {
  return request({
    url: '/order/printer/user',
    method: 'GET'
  });
}

