/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.household-print {
  background-color: #f5f5f5;
  min-height: 100vh;
  padding-bottom: 120rpx;
}
.tip-banner {
  background: linear-gradient(135deg, #FFE082 0%, #FFA726 100%);
  padding: 20rpx;
  margin: 20rpx;
  border-radius: 12rpx;
  text-align: center;
}
.tip-banner .tip-text {
  color: #8D6E63;
  font-size: 26rpx;
  font-weight: 500;
}
.page-card {
  background-color: #fff;
  margin: 20rpx;
  border-radius: 16rpx;
  overflow: hidden;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
}
.page-card .page-header {
  background-color: #f8f9fa;
  padding: 30rpx;
  border-bottom: 2rpx solid #e9ecef;
}
.page-card .page-header .page-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
}
.page-card .page-content {
  position: relative;
  height: 500rpx;
  overflow: hidden;
}
.page-card .page-content:active {
  opacity: 0.9;
}
.household-print__page-image {
  width: 100%;
  height: 100%;
  position: absolute;
  top: 0;
  left: 0;
  object-fit: cover;
}
.household-print__page-placeholder {
  width: 100%;
  height: 100%;
  position: absolute;
  top: 0;
  left: 0;
  background: linear-gradient(135deg, #F5F5F5 0%, #E8E8E8 100%);
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  border: 2rpx dashed #CCCCCC;
  box-sizing: border-box;
}
.household-print__placeholder-text {
  font-size: 28rpx;
  color: #999999;
  font-weight: 500;
  margin-bottom: 8rpx;
}
.household-print__placeholder-desc {
  font-size: 24rpx;
  color: #BBBBBB;
}
.household-print__camera-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.4);
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  gap: 20rpx;
}
.household-print__camera {
  width: 120rpx;
  height: 120rpx;
  background-color: rgba(0, 0, 0, 0.6);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
}
.household-print__text {
  font-size: 32rpx;
  color: #FFFFFF;
  font-weight: 500;
  text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.5);
  text-align: center;
}
.action-buttons {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background-color: #fff;
  padding: 30rpx;
  border-top: 1rpx solid #e9ecef;
  display: flex;
  gap: 20rpx;
}
.action-buttons .btn {
  flex: 1;
  height: 88rpx;
  border-radius: 44rpx;
  font-size: 32rpx;
  font-weight: 500;
  border: none;
}
.action-buttons .btn-secondary {
  background-color: #f8f9fa;
  color: #6c757d;
}
.action-buttons .btn-primary {
  background-color: #FFA500;
  color: #fff;
}
.action-buttons .btn-primary:disabled {
  background-color: #ccc;
  color: #999;
}

/* 适配不同屏幕尺寸 */
@media screen and (max-width: 375px) {
.page-card {
    margin: 15rpx;
}
.page-card .page-content {
    height: 450rpx;
}
.household-print__camera {
    width: 100rpx;
    height: 100rpx;
}
.household-print__text {
    font-size: 28rpx;
}
.household-print__placeholder-text {
    font-size: 24rpx;
}
.household-print__placeholder-desc {
    font-size: 20rpx;
}
.action-buttons {
    padding: 20rpx;
}
.action-buttons .btn {
    height: 80rpx;
    font-size: 28rpx;
}
}