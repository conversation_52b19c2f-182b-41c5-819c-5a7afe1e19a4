"use strict";
const common_vendor = require("../../common/vendor.js");
const _sfc_main = {
  data() {
    return {
      currentSide: "",
      // 'front' 或 'back'
      frontImage: "",
      // 正面拍摄图片
      backImage: "",
      // 背面拍摄图片
      // 身份证模板图片 - 留空供后续引用
      frontCardImage: "",
      // 正面模板图片路径
      backCardImage: ""
      // 背面模板图片路径
    };
  },
  methods: {
    // 返回上一页
    onBackClick() {
      common_vendor.index.navigateBack({
        delta: 1
      });
    },
    // 显示操作选择
    showActionSheet(side) {
      this.currentSide = side;
      common_vendor.index.showActionSheet({
        itemList: ["拍照", "从相册选择"],
        success: (res) => {
          if (res.tapIndex === 0) {
            this.takePhoto();
          } else if (res.tapIndex === 1) {
            this.chooseFromAlbum();
          }
        }
      });
    },
    // 拍照
    takePhoto() {
      common_vendor.index.chooseImage({
        count: 1,
        sourceType: ["camera"],
        success: (res) => {
          common_vendor.index.__f__("log", "at pages/copy/id-card-print.vue:124", "拍照成功:", res.tempFilePaths[0]);
          this.previewPhoto(res.tempFilePaths[0]);
        },
        fail: () => {
          common_vendor.index.showToast({
            title: "拍照取消",
            icon: "none"
          });
        }
      });
    },
    // 从相册选择
    chooseFromAlbum() {
      common_vendor.index.chooseImage({
        count: 1,
        sourceType: ["album"],
        success: (res) => {
          common_vendor.index.__f__("log", "at pages/copy/id-card-print.vue:142", "选择照片成功:", res.tempFilePaths[0]);
          this.previewPhoto(res.tempFilePaths[0]);
        },
        fail: () => {
          common_vendor.index.showToast({
            title: "选择照片取消",
            icon: "none"
          });
        }
      });
    },
    // 处理选择的图片
    previewPhoto(path) {
      if (this.currentSide === "front") {
        this.frontImage = path;
      } else {
        this.backImage = path;
      }
      this.currentSide === "front" ? "正面" : "背面";
      if (this.frontImage && this.backImage) {
        this.proceedToPrintSettings();
      } else {
        const nextSide = this.currentSide === "front" ? "背面" : "正面";
        common_vendor.index.showToast({
          title: `请继续拍摄${nextSide}`,
          icon: "none"
        });
      }
    },
    // 跳转到打印设置
    proceedToPrintSettings() {
      common_vendor.index.showModal({
        title: "拍摄完成",
        content: "身份证正反面已拍摄完成，是否进入打印设置？",
        success: (res) => {
          if (res.confirm) {
            common_vendor.index.navigateTo({});
          }
        }
      });
    },
    // 设置身份证模板图片 - 供后续引用
    setIdCardTemplateImages(frontImageUrl, backImageUrl) {
      this.frontCardImage = frontImageUrl;
      this.backCardImage = backImageUrl;
    }
  },
  // 页面加载时的处理
  onLoad() {
  }
};
if (!Array) {
  const _easycom_uni_icons2 = common_vendor.resolveComponent("uni-icons");
  const _component_f_page_layout = common_vendor.resolveComponent("f-page-layout");
  (_easycom_uni_icons2 + _component_f_page_layout)();
}
const _easycom_uni_icons = () => "../../uni_modules/uni-icons/components/uni-icons/uni-icons.js";
if (!Math) {
  _easycom_uni_icons();
}
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return common_vendor.e({
    a: $data.frontCardImage
  }, $data.frontCardImage ? {
    b: $data.frontCardImage
  } : {}, {
    c: common_vendor.p({
      type: "camera",
      color: "#FFFFFF",
      size: "30"
    }),
    d: common_vendor.o(($event) => $options.showActionSheet("front")),
    e: $data.backCardImage
  }, $data.backCardImage ? {
    f: $data.backCardImage
  } : {}, {
    g: common_vendor.p({
      type: "camera",
      color: "#FFFFFF",
      size: "30"
    }),
    h: common_vendor.o(($event) => $options.showActionSheet("back")),
    i: common_vendor.o($options.onBackClick),
    j: common_vendor.p({
      title: "身份证/居住证拍照打印",
      ["fallback-path"]: "/pages/copy/index"
    })
  });
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages/copy/id-card-print.js.map
