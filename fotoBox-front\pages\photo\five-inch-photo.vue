<template>
	<f-page-layout title="5寸照片" back>
		<view class="five-inch-photo">
			<!-- 空状态展示 -->
			<view class="empty-state" v-if="photoList.length === 0">
				<image class="empty-box" src="/static/images/icons/Null.png" mode="aspectFit"></image>
				<text class="empty-text">暂无照片哦~</text>
			</view>
			
			<!-- 照片列表 -->
			<view class="file-list-container" v-else>
				<scroll-view class="file-list-scroll" scroll-y>
					<view class="file-list">
						<view class="file-item" v-for="(photo, index) in photoList" :key="index">
							<!-- 上传中状态 -->
							<view class="file-card uploading" v-if="photo.uploading">
								<view class="file-name">{{photo.name || '5寸照片_' + (index + 1)}}</view>
								<view class="upload-progress-text">照片上传中</view>
								<view class="upload-progress-bar-container">
									<view class="upload-progress-bar" :style="{width: photo.progress + '%'}"></view>
								</view>
								<view class="upload-progress-value">{{photo.progress}}%</view>
								<view class="delete-btn" @click.stop="deletePhoto(index)">
									<uni-icons type="trash" size="24" color="#333333"></uni-icons>
									<text>删除</text>
								</view>
							</view>
							
							<!-- 上传完成状态 - 打印设置卡片 -->
							<view class="file-card print-settings" v-else>
								<view class="file-header">
									<text class="file-name">{{photo.name || '5寸照片_' + (index + 1)}}</text>
									<image class="photo-thumbnail" :src="photo.path" mode="aspectFill"></image>
								</view>
								
								<!-- 打印设置选项 -->
								<view class="setting-item">
									<text class="setting-label">打印份数</text>
									<view class="copies-selector">
										<view class="copies-btn" @click="decreaseCopies(index)">-</view>
										<input class="copies-input" type="number" v-model="photo.copies" />
										<view class="copies-btn" @click="increaseCopies(index)">+</view>
									</view>
								</view>
								
								<view class="setting-item">
									<text class="setting-label">打印页面</text>
									<view class="option-selector">
										<view class="option-btn" :class="{'active': photo.printMode === 'single'}" @click="setPrintMode(index, 'single')">
											单面
										</view>
										<view class="option-btn" :class="{'active': photo.printMode === 'double'}" @click="setPrintMode(index, 'double')">
											双面
										</view>
									</view>
								</view>
								
								<view class="setting-item">
									<text class="setting-label">打印类型</text>
									<view class="option-selector">
										<view class="option-btn" :class="{'active': photo.printType === 'black-white'}" @click="setPrintType(index, 'black-white')">
											黑白
										</view>
										<view class="option-btn" :class="{'active': photo.printType === 'color'}" @click="setPrintType(index, 'color')">
											彩色
										</view>
									</view>
								</view>
								
								<view class="file-actions">
									<view class="action-btn" @click="previewPhoto(photo)">
										<uni-icons type="eye" size="20" color="#FFA500"></uni-icons>
										<text>预览</text>
									</view>
									<view class="action-btn" @click="deletePhoto(index)">
										<uni-icons type="trash" size="20" color="#333333"></uni-icons>
										<text>删除</text>
									</view>
								</view>
							</view>
						</view>
					</view>
				</scroll-view>
			</view>
			
			<!-- 底部固定区域 -->
			<view class="fixed-bottom-area">
				<view class="notice-text">
					严禁打印涉及反动、淫秽、暴恐、色情、侵权等非法内容
				</view>
				
				<view class="btn-group">
					<button class="select-btn" @click="addPhoto">添加照片</button>
					<button class="print-btn" @click="goPrint" :disabled="photoList.length === 0">下单打印</button>
				</view>
			</view>
		</view>
	</f-page-layout>
</template>

<script>
export default {
	data() {
		return {
			photoList: []
		}
	},
	
	methods: {
		// 添加照片 - 支持拍照和相册选择
		addPhoto() {
			uni.showActionSheet({
				itemList: ['拍照', '从相册选择'],
				success: (res) => {
					if (res.tapIndex === 0) {
						this.takePhoto();
					} else if (res.tapIndex === 1) {
						this.selectPhotoFromAlbum();
					}
				}
			});
		},
		
		// 拍照
		takePhoto() {
			uni.chooseImage({
				count: 1,
				sourceType: ['camera'],
				success: (res) => {
					this.handleSelectedPhotos(res.tempFilePaths);
				},
				fail: (err) => {
					console.error('拍照失败:', err);
					uni.showToast({
						title: '拍照失败',
						icon: 'none'
					});
				}
			});
		},
		
		// 选择手机相册照片
		selectPhotoFromAlbum() {
			uni.chooseImage({
				count: 9, // 最多选择9张
				sourceType: ['album'],
				success: (res) => {
					this.handleSelectedPhotos(res.tempFilePaths);
				},
				fail: (err) => {
					console.error('选择照片失败:', err);
					uni.showToast({
						title: '选择照片失败',
						icon: 'none'
					});
				}
			});
		},
		
		// 处理选择的照片
		handleSelectedPhotos(filePaths) {
			filePaths.forEach((path, index) => {
				const photo = {
					path: path,
					name: `5寸照片_${this.photoList.length + index + 1}`,
					copies: 1,
					printMode: 'single',
					printType: 'color',
					uploading: true,
					progress: 0
				};
				
				this.photoList.push(photo);
				this.simulateUpload(this.photoList.length - 1);
			});
		},
		
		// 模拟上传进度
		simulateUpload(index) {
			const photo = this.photoList[index];
			const interval = setInterval(() => {
				photo.progress += Math.random() * 20;
				if (photo.progress >= 100) {
					photo.progress = 100;
					photo.uploading = false;
					clearInterval(interval);
				}
			}, 200);
		},
		
		// 增加份数
		increaseCopies(index) {
			if (this.photoList[index].copies < 99) {
				this.photoList[index].copies++;
			}
		},
		
		// 减少份数
		decreaseCopies(index) {
			if (this.photoList[index].copies > 1) {
				this.photoList[index].copies--;
			}
		},
		
		// 设置打印模式
		setPrintMode(index, mode) {
			this.photoList[index].printMode = mode;
		},
		
		// 设置打印类型
		setPrintType(index, type) {
			this.photoList[index].printType = type;
		},
		
		// 预览照片
		previewPhoto(photo) {
			uni.previewImage({
				urls: [photo.path],
				current: photo.path
			});
		},
		
		// 删除照片
		deletePhoto(index) {
			uni.showModal({
				title: '确认删除',
				content: '确定要删除这张照片吗？',
				success: (res) => {
					if (res.confirm) {
						this.photoList.splice(index, 1);
					}
				}
			});
		},
		
		// 去打印
		goPrint() {
			if (this.photoList.length === 0) {
				uni.showToast({
					title: '请先添加照片',
					icon: 'none'
				});
				return;
			}
			
			// 检查是否有正在上传的照片
			const uploadingPhotos = this.photoList.filter(photo => photo.uploading);
			if (uploadingPhotos.length > 0) {
				uni.showToast({
					title: '请等待照片上传完成',
					icon: 'none'
				});
				return;
			}
			
			// 计算总份数和价格
			const totalCopies = this.photoList.reduce((sum, photo) => sum + photo.copies, 0);
			const pricePerCopy = 1.5; // 5寸照片单价
			const totalPrice = totalCopies * pricePerCopy;
			
			// 显示打印确认信息
			uni.showModal({
				title: '确认打印',
				content: `共${this.photoList.length}张照片，${totalCopies}份，预计费用：¥${totalPrice.toFixed(2)}`,
				confirmText: '确认打印',
				cancelText: '取消',
				success: (res) => {
					if (res.confirm) {
						// 这里可以调用打印API或跳转到支付页面
						uni.showToast({
							title: '打印订单已提交',
							icon: 'success'
						});
						
						// 可以跳转到订单页面
						setTimeout(() => {
							uni.navigateBack();
						}, 1500);
					}
				}
			});
		}
	}
}
</script>

<style lang="scss" scoped>
.five-inch-photo {
	background-color: #f5f5f5;
	min-height: 100vh;
	padding-bottom: 200rpx; // 为底部固定区域留出空间

	.empty-state {
		display: flex;
		flex-direction: column;
		align-items: center;
		justify-content: center;
		padding: 200rpx 40rpx;

		.empty-box {
			width: 200rpx;
			height: 200rpx;
			margin-bottom: 40rpx;
		}

		.empty-text {
			font-size: 28rpx;
			color: #999999;
		}
	}

	.file-list-container {
		height: calc(100vh - 200rpx);

		.file-list-scroll {
			height: 100%;

			.file-list {
				padding: 20rpx;

				.file-item {
					margin-bottom: 20rpx;

					.file-card {
						background-color: #ffffff;
						border-radius: 12rpx;
						padding: 30rpx;
						box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);

						&.uploading {
							.file-name {
								font-size: 28rpx;
								color: #333;
								font-weight: 500;
								margin-bottom: 20rpx;
							}

							.upload-progress-text {
								font-size: 24rpx;
								color: #666;
								margin-bottom: 10rpx;
							}

							.upload-progress-bar-container {
								width: 100%;
								height: 8rpx;
								background-color: #f0f0f0;
								border-radius: 4rpx;
								margin-bottom: 10rpx;
								overflow: hidden;

								.upload-progress-bar {
									height: 100%;
									background-color: #007aff;
									border-radius: 4rpx;
									transition: width 0.3s ease;
								}
							}

							.upload-progress-value {
								font-size: 24rpx;
								color: #007aff;
								text-align: right;
								margin-bottom: 20rpx;
							}

							.delete-btn {
								display: flex;
								flex-direction: column;
								align-items: center;
								justify-content: center;
								width: 100rpx;
								height: 80rpx;
								margin-left: auto;

								text {
									font-size: 24rpx;
									color: #333;
									margin-top: 4rpx;
								}
							}
						}

						&.print-settings {
							.file-header {
								display: flex;
								justify-content: space-between;
								align-items: center;
								padding-bottom: 20rpx;
								border-bottom: 1px solid #f0f0f0;

								.file-name {
									font-size: 28rpx;
									color: #333;
									font-weight: 500;
									max-width: 70%;
									white-space: nowrap;
									overflow: hidden;
									text-overflow: ellipsis;
								}

								.photo-thumbnail {
									width: 80rpx;
									height: 80rpx;
									border-radius: 6rpx;
									object-fit: cover;
								}
							}

							.setting-item {
								display: flex;
								justify-content: space-between;
								align-items: center;
								padding: 20rpx 0;
								border-bottom: 1px solid #f0f0f0;

								.setting-label {
									font-size: 28rpx;
									color: #333;
								}

								.copies-selector {
									display: flex;
									align-items: center;
									background-color: #f5f5f5;
									border-radius: 6rpx;

									.copies-btn {
										width: 60rpx;
										height: 60rpx;
										display: flex;
										justify-content: center;
										align-items: center;
										font-size: 32rpx;
										color: #333;
										background-color: #f0f0f0;
									}

									.copies-input {
										width: 80rpx;
										height: 60rpx;
										text-align: center;
										font-size: 28rpx;
										color: #333;
										background-color: #ffffff;
									}
								}

								.option-selector {
									display: flex;
									gap: 10rpx;
								}

								.option-btn {
									width: 100rpx;
									height: 70rpx;
									display: flex;
									justify-content: center;
									align-items: center;
									background-color: #f5f5f5;
									color: #999;
									border-radius: 6rpx;
									font-size: 28rpx;

									&.active {
										background-color: #7D7DFF;
										color: #ffffff;
									}
								}
							}

							.file-actions {
								display: flex;
								justify-content: flex-end;
								padding-top: 20rpx;

								.action-btn {
									display: flex;
									flex-direction: column;
									align-items: center;
									margin-left: 40rpx;

									text {
										font-size: 24rpx;
										color: #333;
										margin-top: 4rpx;
									}
								}
							}
						}
					}
				}
			}
		}
	}

	.fixed-bottom-area {
		position: fixed;
		bottom: 0;
		left: 0;
		right: 0;
		width: 100%;
		background-color: #f8f8f8;
		box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.05);
		z-index: 10;
		padding-bottom: env(safe-area-inset-bottom);

		.notice-text {
			font-size: 24rpx;
			color: #ff5a5f;
			text-align: center;
			padding: 20rpx;
			line-height: 1.5;
		}

		.btn-group {
			display: flex;
			justify-content: space-between;
			padding: 0 30rpx 30rpx;

			.select-btn {
				flex: 1;
				height: 80rpx;
				line-height: 80rpx;
				background-color: #ffffff;
				color: #7D7DFF;
				border: 1px solid #7D7DFF;
				border-radius: 40rpx;
				font-size: 28rpx;
				margin-right: 20rpx;
			}

			.print-btn {
				flex: 1;
				height: 80rpx;
				line-height: 80rpx;
				background-color: #CCCCCC;
				color: #ffffff;
				border-radius: 40rpx;
				font-size: 28rpx;

				&:not([disabled]) {
					background-color: #007aff;
				}
			}
		}
	}
}
</style>
