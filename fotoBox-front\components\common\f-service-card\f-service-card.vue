<template>
	<view class="f-service-card" @click="onClick">
		<view class="f-service-card__icon" :style="{ color: iconColor }">
			<image v-if="iconType === 'image'" :src="icon" class="f-service-card__icon-image" />
			<uni-icons v-else class="f-service-card__icon-uni" :type="icon" :size="35" :color="iconColor"></uni-icons>
		</view>
		<text class="f-service-card__title">{{ title }}</text>
	</view>
</template>

<script>
	export default {
		name: 'FServiceCard',
		props: {
			title: {
				type: String,
				required: true
			},
			icon: {
				type: String,
				required: true
			},
			iconType: {
				type: String,
				default: 'uni', // 'uni' 或 'image'
				validator: value => ['uni', 'image'].includes(value)
			},
			iconColor: {
				type: String,
				default: '#409EFF'
			}
		},
		methods: {
			onClick() {
				this.$emit('click');
			}
		}
	}
</script>

<style lang="scss">
	.f-service-card {
		background-color: #fff;
		border-radius: 12rpx;
		padding: 30rpx 20rpx;
		display: flex;
		flex-direction: column;
		align-items: center;
		justify-content: center;
		box-shadow: 0 2rpx 6rpx rgba(0, 0, 0, 0.05);
		transition: all 0.3s;
		height: 170rpx;
		box-sizing: border-box;
		
		&:active {
			transform: scale(0.98);
			opacity: 0.9;
		}
		
		&__icon {
			margin-bottom: 16rpx;
			display: flex;
			justify-content: center;
			align-items: center;
			width: 80rpx;
			height: 80rpx;
			
			&-image {
				width: 100%;
				height: 100%;
			}
			
			&-uni {
				/* uni-icons会自动设置大小 */
			}
		}
		
		&__title {
			font-size: 28rpx;
			color: #333;
			font-weight: 500;
		}
	}
</style> 