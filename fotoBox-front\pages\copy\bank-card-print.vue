<template>
    <f-page-layout
        title="银行卡/社保卡拍照打印"
        fallback-path="/pages/copy/index"
        @back-click="onBackClick"
    >
        <view class="bank-card-print">
            <!-- 顶部提示 -->
            <view class="bank-card-print__header">
                <text class="bank-card-print__tip">内容拍照时需要与背景颜色尽量区分</text>
            </view>
            
            <view class="bank-card-print__content">
                <!-- 正面拍摄区域 -->
                <view class="bank-card-print__card-container" @click="showActionSheet('front')">
                    <view class="bank-card-print__card bank-card-print__card--front">
                        <!-- 银行卡正面背景图片 -->
                        <image
                            v-if="frontCardImage"
                            class="bank-card-print__card-image"
                            :src="frontCardImage"
                            mode="aspectFill"
                        />
                        <!-- 无图片时的占位符 -->
                        <view v-else class="bank-card-print__card-placeholder">
                            <view class="bank-card-print__placeholder-text">银行卡正面模板</view>
                            <view class="bank-card-print__placeholder-desc">图片将在此处显示</view>
                        </view>

                        <!-- 相机图标覆盖层 -->
                        <view class="bank-card-print__camera-overlay">
                            <view class="bank-card-print__camera-icon">
                                <uni-icons type="camera" color="#FFFFFF" size="40"></uni-icons>
                            </view>
                            <view class="bank-card-print__camera-text">拍摄正面</view>
                        </view>
                    </view>
                </view>
                
                <!-- 背面拍摄区域 -->
                <view class="bank-card-print__card-container" @click="showActionSheet('back')">
                    <view class="bank-card-print__card bank-card-print__card--back">
                        <!-- 银行卡背面背景图片 -->
                        <image
                            v-if="backCardImage"
                            class="bank-card-print__card-image"
                            :src="backCardImage"
                            mode="aspectFill"
                        />
                        <!-- 无图片时的占位符 -->
                        <view v-else class="bank-card-print__card-placeholder">
                            <view class="bank-card-print__placeholder-text">银行卡背面模板</view>
                            <view class="bank-card-print__placeholder-desc">图片将在此处显示</view>
                        </view>

                        <!-- 相机图标覆盖层 -->
                        <view class="bank-card-print__camera-overlay">
                            <view class="bank-card-print__camera-icon">
                                <uni-icons type="camera" color="#FFFFFF" size="40"></uni-icons>
                            </view>
                            <view class="bank-card-print__camera-text">拍摄背面</view>
                        </view>
                    </view>
                </view>
            </view>
        </view>
    </f-page-layout>
</template>

<script>
export default {
    data() {
        return {
            currentSide: '', // 'front' 或 'back'
            frontImage: '', // 正面拍摄图片
            backImage: '', // 背面拍摄图片
            // 银行卡模板图片 - 留空供后续引用
            frontCardImage: '', // 正面模板图片路径
            backCardImage: '' // 背面模板图片路径
        }
    },
    methods: {
        // 返回上一页
        onBackClick() {
            uni.navigateBack({
                delta: 1
            });
        },
        
        // 显示操作选择
        showActionSheet(side) {
            this.currentSide = side;
            const sideText = side === 'front' ? '正面' : '背面';
            
            uni.showActionSheet({
                itemList: ['拍照', '从相册选择'],
                success: (res) => {
                    if (res.tapIndex === 0) {
                        this.takePhoto();
                    } else if (res.tapIndex === 1) {
                        this.chooseFromAlbum();
                    }
                }
            });
        },
        
        // 拍照
        takePhoto() {
            uni.chooseImage({
                count: 1,
                sourceType: ['camera'],
                success: (res) => {
                    this.handleImageSelected(res.tempFilePaths[0]);
                },
                fail: (err) => {
                    console.error('拍照失败:', err);
                    uni.showToast({
                        title: '拍照失败',
                        icon: 'none'
                    });
                }
            });
        },
        
        // 从相册选择
        chooseFromAlbum() {
            uni.chooseImage({
                count: 1,
                sourceType: ['album'],
                success: (res) => {
                    this.handleImageSelected(res.tempFilePaths[0]);
                },
                fail: (err) => {
                    console.error('选择图片失败:', err);
                    uni.showToast({
                        title: '选择图片失败',
                        icon: 'none'
                    });
                }
            });
        },
        
        // 处理选择的图片
        handleImageSelected(imagePath) {
            if (this.currentSide === 'front') {
                this.frontImage = imagePath;
            } else {
                this.backImage = imagePath;
            }
            
            // 检查是否两面都已拍摄
            if (this.frontImage && this.backImage) {
                this.proceedToPrintSettings();
            } else {
                const nextSide = this.currentSide === 'front' ? '背面' : '正面';
                uni.showToast({
                    title: `请继续拍摄${nextSide}`,
                    icon: 'none'
                });
            }
        },
        
        // 跳转到打印设置
        proceedToPrintSettings() {
            uni.showModal({
                title: '拍摄完成',
                content: '银行卡正反面已拍摄完成，是否进入打印设置？',
                success: (res) => {
                    if (res.confirm) {
                        
                    }
                }
            });
        },

        // 设置银行卡模板图片 - 供后续引用
        setBankCardTemplateImages(frontImageUrl, backImageUrl) {
            this.frontCardImage = frontImageUrl;
            this.backCardImage = backImageUrl;
        }
    },

    // 页面加载时的处理
    onLoad() {
        // 这里可以设置默认的银行卡模板图片
        // 示例：this.setBankCardTemplateImages('/static/images/bank-card-front.png', '/static/images/bank-card-back.png');
    }
}
</script>

<style lang="scss">
.bank-card-print {
    padding: 20rpx;
    
    &__header {
        background: linear-gradient(135deg, #FFE4B5 0%, #FFF8DC 100%);
        padding: 20rpx;
        border-radius: 12rpx;
        margin-bottom: 30rpx;
        text-align: center;
    }
    
    &__tip {
        font-size: 28rpx;
        color: #D2691E;
        font-weight: 500;
    }
    
    &__content {
        display: flex;
        flex-direction: column;
        gap: 30rpx;
    }
    
    &__card-container {
        position: relative;
        border-radius: 20rpx;
        overflow: hidden;
        box-shadow: 0 8rpx 30rpx rgba(0, 0, 0, 0.1);
        
        &:active {
            transform: scale(0.98);
            transition: transform 0.1s ease;
        }
    }
    
    &__card {
        position: relative;
        width: 100%;
        height: 400rpx;
        border-radius: 20rpx;
        overflow: hidden;
    }
    
    &__card-image {
        width: 100%;
        height: 100%;
        position: absolute;
        top: 0;
        left: 0;
        border-radius: 20rpx;
        object-fit: cover;
    }

    &__card-placeholder {
        width: 100%;
        height: 100%;
        position: absolute;
        top: 0;
        left: 0;
        border-radius: 20rpx;
        background: linear-gradient(135deg, #F5F5F5 0%, #E8E8E8 100%);
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        border: 2rpx dashed #CCCCCC;
        box-sizing: border-box;
    }

    &__placeholder-text {
        font-size: 28rpx;
        color: #999999;
        font-weight: 500;
        margin-bottom: 8rpx;
    }

    &__placeholder-desc {
        font-size: 24rpx;
        color: #BBBBBB;
    }
    

    
    // 相机覆盖层
    &__camera-overlay {
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: rgba(0, 0, 0, 0.5);
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        border-radius: 20rpx;
    }
    
    &__camera-icon {
        width: 120rpx;
        height: 120rpx;
        background: rgba(0, 0, 0, 0.6);
        border-radius: 50%;
        display: flex;
        justify-content: center;
        align-items: center;
        margin-bottom: 20rpx;
    }
    
    &__camera-text {
        font-size: 32rpx;
        color: #fff;
        font-weight: 500;
        text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.5);
    }
}

/* 适配不同屏幕尺寸 */
@media screen and (max-width: 375px) {
    .bank-card-print {
        padding: 15rpx;
        
        &__card {
            height: 350rpx;
        }
        
        &__card-bg {
            padding: 25rpx;
        }
        
        &__bank-logo {
            font-size: 28rpx;
        }
        
        &__bank-name {
            font-size: 24rpx;
        }
        
        &__number-group {
            font-size: 28rpx;
        }
    }
}
</style>
