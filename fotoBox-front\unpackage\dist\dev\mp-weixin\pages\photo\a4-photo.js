"use strict";
const common_vendor = require("../../common/vendor.js");
const common_assets = require("../../common/assets.js");
const _sfc_main = {
  data() {
    return {
      photoList: []
    };
  },
  methods: {
    // 添加照片 - 支持拍照和相册选择
    addPhoto() {
      common_vendor.index.showActionSheet({
        itemList: ["拍照", "从相册选择"],
        success: (res) => {
          if (res.tapIndex === 0) {
            this.takePhoto();
          } else if (res.tapIndex === 1) {
            this.selectPhotoFromAlbum();
          }
        }
      });
    },
    // 拍照
    takePhoto() {
      common_vendor.index.chooseImage({
        count: 1,
        sourceType: ["camera"],
        success: (res) => {
          this.handleSelectedPhotos(res.tempFilePaths);
        },
        fail: (err) => {
          common_vendor.index.__f__("error", "at pages/photo/a4-photo.vue:145", "拍照失败:", err);
          common_vendor.index.showToast({
            title: "拍照失败",
            icon: "none"
          });
        }
      });
    },
    // 选择手机相册照片
    selectPhotoFromAlbum() {
      common_vendor.index.chooseImage({
        count: 9,
        // 最多选择9张
        sourceType: ["album"],
        success: (res) => {
          this.handleSelectedPhotos(res.tempFilePaths);
        },
        fail: (err) => {
          common_vendor.index.__f__("error", "at pages/photo/a4-photo.vue:163", "选择照片失败:", err);
          common_vendor.index.showToast({
            title: "选择照片失败",
            icon: "none"
          });
        }
      });
    },
    // 处理选择的照片
    handleSelectedPhotos(filePaths) {
      filePaths.forEach((path, index) => {
        const photo = {
          path,
          name: `A4图文_${this.photoList.length + index + 1}`,
          copies: 1,
          printMode: "single",
          printType: "color",
          paperSize: "a4",
          uploading: true,
          progress: 0
        };
        this.photoList.push(photo);
        this.simulateUpload(this.photoList.length - 1);
      });
    },
    // 模拟上传进度
    simulateUpload(index) {
      const photo = this.photoList[index];
      const interval = setInterval(() => {
        photo.progress += Math.random() * 20;
        if (photo.progress >= 100) {
          photo.progress = 100;
          photo.uploading = false;
          clearInterval(interval);
        }
      }, 200);
    },
    // 增加份数
    increaseCopies(index) {
      if (this.photoList[index].copies < 99) {
        this.photoList[index].copies++;
      }
    },
    // 减少份数
    decreaseCopies(index) {
      if (this.photoList[index].copies > 1) {
        this.photoList[index].copies--;
      }
    },
    // 设置打印模式
    setPrintMode(index, mode) {
      this.photoList[index].printMode = mode;
    },
    // 设置打印类型
    setPrintType(index, type) {
      this.photoList[index].printType = type;
    },
    // 设置纸张尺寸
    setPaperSize(index, size) {
      this.photoList[index].paperSize = size;
    },
    // 预览照片
    previewPhoto(photo) {
      common_vendor.index.previewImage({
        urls: [photo.path],
        current: photo.path
      });
    },
    // 删除照片
    deletePhoto(index) {
      common_vendor.index.showModal({
        title: "确认删除",
        content: "确定要删除这张照片吗？",
        success: (res) => {
          if (res.confirm) {
            this.photoList.splice(index, 1);
          }
        }
      });
    },
    // 去打印
    goPrint() {
      if (this.photoList.length === 0) {
        common_vendor.index.showToast({
          title: "请先添加照片",
          icon: "none"
        });
        return;
      }
      const uploadingPhotos = this.photoList.filter((photo) => photo.uploading);
      if (uploadingPhotos.length > 0) {
        common_vendor.index.showToast({
          title: "请等待照片上传完成",
          icon: "none"
        });
        return;
      }
      const totalCopies = this.photoList.reduce((sum, photo) => sum + photo.copies, 0);
      const pricePerCopy = 2;
      const totalPrice = totalCopies * pricePerCopy;
      common_vendor.index.showModal({
        title: "确认打印",
        content: `共${this.photoList.length}张照片，${totalCopies}份，预计费用：¥${totalPrice.toFixed(2)}`,
        confirmText: "确认打印",
        cancelText: "取消",
        success: (res) => {
          if (res.confirm) {
            common_vendor.index.showToast({
              title: "打印订单已提交",
              icon: "success"
            });
            setTimeout(() => {
              common_vendor.index.navigateBack();
            }, 1500);
          }
        }
      });
    }
  }
};
if (!Array) {
  const _easycom_uni_icons2 = common_vendor.resolveComponent("uni-icons");
  const _component_f_page_layout = common_vendor.resolveComponent("f-page-layout");
  (_easycom_uni_icons2 + _component_f_page_layout)();
}
const _easycom_uni_icons = () => "../../uni_modules/uni-icons/components/uni-icons/uni-icons.js";
if (!Math) {
  _easycom_uni_icons();
}
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return common_vendor.e({
    a: $data.photoList.length === 0
  }, $data.photoList.length === 0 ? {
    b: common_assets._imports_0$1
  } : {
    c: common_vendor.f($data.photoList, (photo, index, i0) => {
      return common_vendor.e({
        a: photo.uploading
      }, photo.uploading ? {
        b: common_vendor.t(photo.name || "A4图文_" + (index + 1)),
        c: photo.progress + "%",
        d: common_vendor.t(photo.progress),
        e: "c9393d14-1-" + i0 + ",c9393d14-0",
        f: common_vendor.p({
          type: "trash",
          size: "24",
          color: "#333333"
        }),
        g: common_vendor.o(($event) => $options.deletePhoto(index), index)
      } : {
        h: common_vendor.t(photo.name || "A4图文_" + (index + 1)),
        i: photo.path,
        j: common_vendor.o(($event) => $options.decreaseCopies(index), index),
        k: photo.copies,
        l: common_vendor.o(($event) => photo.copies = $event.detail.value, index),
        m: common_vendor.o(($event) => $options.increaseCopies(index), index),
        n: photo.printMode === "single" ? 1 : "",
        o: common_vendor.o(($event) => $options.setPrintMode(index, "single"), index),
        p: photo.printMode === "double" ? 1 : "",
        q: common_vendor.o(($event) => $options.setPrintMode(index, "double"), index),
        r: photo.printType === "black-white" ? 1 : "",
        s: common_vendor.o(($event) => $options.setPrintType(index, "black-white"), index),
        t: photo.printType === "color" ? 1 : "",
        v: common_vendor.o(($event) => $options.setPrintType(index, "color"), index),
        w: photo.paperSize === "a4" ? 1 : "",
        x: common_vendor.o(($event) => $options.setPaperSize(index, "a4"), index),
        y: photo.paperSize === "a3" ? 1 : "",
        z: common_vendor.o(($event) => $options.setPaperSize(index, "a3"), index),
        A: "c9393d14-2-" + i0 + ",c9393d14-0",
        B: common_vendor.p({
          type: "eye",
          size: "20",
          color: "#FFA500"
        }),
        C: common_vendor.o(($event) => $options.previewPhoto(photo), index),
        D: "c9393d14-3-" + i0 + ",c9393d14-0",
        E: common_vendor.p({
          type: "trash",
          size: "20",
          color: "#333333"
        }),
        F: common_vendor.o(($event) => $options.deletePhoto(index), index)
      }, {
        G: index
      });
    })
  }, {
    d: common_vendor.o((...args) => $options.addPhoto && $options.addPhoto(...args)),
    e: common_vendor.o((...args) => $options.goPrint && $options.goPrint(...args)),
    f: $data.photoList.length === 0,
    g: common_vendor.p({
      title: "A4图文",
      back: true
    })
  });
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render], ["__scopeId", "data-v-c9393d14"]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages/photo/a4-photo.js.map
