{"version": 3, "file": "app.js", "sources": ["App.vue", "main.js"], "sourcesContent": ["<script>\n\texport default {\n\t\tonLaunch: function() {\n\t\t\tconsole.log('App Launch')\n\t\t\t\n\t\t\t// 获取系统状态栏高度\n\t\t\tthis.setStatusBarHeight()\n\t\t},\n\t\tonShow: function() {\n\t\t\tconsole.log('App Show')\n\t\t},\n\t\tonHide: function() {\n\t\t\tconsole.log('App Hide')\n\t\t},\n\t\tmethods: {\n\t\t\t// 设置状态栏高度\n\t\t\tsetStatusBarHeight() {\n\t\t\t\t// 获取系统信息\n\t\t\t\tconst systemInfo = uni.getSystemInfoSync()\n\t\t\t\t\n\t\t\t\t// 设置状态栏高度\n\t\t\t\tconst statusBarHeight = systemInfo.statusBarHeight + 'px'\n\t\t\t\tconsole.log('状态栏高度:', statusBarHeight)\n\t\t\t\t\n\t\t\t\t// 设置CSS变量\n\t\t\t\tdocument.documentElement.style.setProperty('--status-bar-height', statusBarHeight)\n\t\t\t\t\n\t\t\t\t// 微信小程序环境，通过uni全局对象保存\n\t\t\t\tuni.setStorageSync('statusBarHeight', statusBarHeight)\n\t\t\t}\n\t\t}\n\t}\n</script>\n\n<style>\n\t/*每个页面公共css */\n\t\n\t/* 引入uni-icons样式 */\n\t@import '@/uni_modules/uni-icons/components/uni-icons/uniicons.css';\n\t\n\t/* 确保uni-icons字体正确加载 */\n\t@font-face {\n\t\tfont-family: uniicons;\n\t\tsrc: url('/uni_modules/uni-icons/components/uni-icons/uniicons.ttf') format('truetype');\n\t\tfont-weight: normal;\n\t\tfont-style: normal;\n\t}\n\t\n\t/* 设置uni-icons的样式 */\n\t.uni-icons {\n\t\tfont-family: uniicons;\n\t\ttext-decoration: none;\n\t\ttext-align: center;\n\t\tline-height: 1;\n\t}\n\t\n\t/* 覆盖uni-app默认tabbar样式，彻底隐藏它 - 在所有环境下 */\n\t.uni-tabbar {\n\t\tdisplay: none !important;\n\t\theight: 0 !important;\n\t\tpadding: 0 !important;\n\t\tmargin: 0 !important;\n\t\ttransform: translateY(100%) !important;\n\t\topacity: 0 !important;\n\t\tpointer-events: none !important;\n\t\tz-index: -1 !important;\n\t\tvisibility: hidden !important;\n\t}\n\t\n\t.uni-tabbar-placeholder {\n\t\theight: 0 !important;\n\t\tpadding: 0 !important;\n\t\tmargin: 0 !important;\n\t}\n\t\n\t.uni-app--showtabbar uni-page-wrapper {\n\t\theight: calc(100% - 0px) !important;\n\t}\n\t\n\t.uni-page-head,\n\t.uni-placeholder,\n\t.uni-status-bar {\n\t\tdisplay: none;\n\t}\n\t\n\t/* 全局变量 */\n\t:root {\n\t\t--status-bar-height: 0px;\n\t}\n</style>\n", "import App from './App'\n\n// #ifndef VUE3\nimport Vue from 'vue'\nimport './uni.promisify.adaptor'\nVue.config.productionTip = false\nApp.mpType = 'app'\nconst app = new Vue({\n  ...App\n})\napp.$mount()\n// #endif\n\n// #ifdef VUE3\nimport { createSSRApp } from 'vue'\nimport store from './store'\nimport api from './api'\n\n// 导入uni-icons组件\nimport uniIcons from '@/uni_modules/uni-icons/components/uni-icons/uni-icons.vue'\n\n// 全局组件\nimport FHeader from './components/common/f-navbar/f-header.vue'\nimport FTabbar from './components/common/f-navbar/f-tabbar.vue'\nimport FPageHeader from './components/common/f-navbar/f-page-header.vue'\nimport FServiceCard from './components/common/f-service-card/f-service-card.vue'\nimport FTabLayout from './components/common/f-layout/f-tab-layout.vue'\nimport FPageLayout from './components/common/f-layout/f-page-layout.vue'\n\nexport function createApp() {\n  const app = createSSRApp(App)\n  \n  // 挂载Vuex\n  app.use(store)\n  \n  // 注册全局组件\n  app.component('uni-icons', uniIcons)\n  app.component('f-header', FHeader)\n  app.component('f-tabbar', FTabbar)\n  app.component('f-page-header', FPageHeader)\n  app.component('f-service-card', FServiceCard)\n  app.component('f-tab-layout', FTabLayout)\n  app.component('f-page-layout', FPageLayout)\n  \n  // 挂载全局API\n  app.config.globalProperties.$api = api\n  \n  return {\n    app\n  }\n}\n// #endif"], "names": ["uni", "createSSRApp", "App", "store", "api"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AACC,MAAK,YAAU;AAAA,EACd,UAAU,WAAW;AACpBA,kBAAAA,MAAY,MAAA,OAAA,gBAAA,YAAY;AAGxB,SAAK,mBAAmB;AAAA,EACxB;AAAA,EACD,QAAQ,WAAW;AAClBA,kBAAAA,MAAY,MAAA,OAAA,iBAAA,UAAU;AAAA,EACtB;AAAA,EACD,QAAQ,WAAW;AAClBA,kBAAAA,MAAY,MAAA,OAAA,iBAAA,UAAU;AAAA,EACtB;AAAA,EACD,SAAS;AAAA;AAAA,IAER,qBAAqB;AAEpB,YAAM,aAAaA,cAAG,MAAC,kBAAkB;AAGzC,YAAM,kBAAkB,WAAW,kBAAkB;AACrDA,oBAAAA,MAAA,MAAA,OAAA,iBAAY,UAAU,eAAe;AAGrC,eAAS,gBAAgB,MAAM,YAAY,uBAAuB,eAAe;AAGjFA,0BAAI,eAAe,mBAAmB,eAAe;AAAA,IACtD;AAAA,EACD;AACD;ACZD,MAAM,WAAW,MAAW;AAG5B,MAAM,UAAU,MAAW;AAC3B,MAAM,UAAU,MAAW;AAC3B,MAAM,cAAc,MAAW;AAC/B,MAAM,eAAe,MAAW;AAChC,MAAM,aAAa,MAAW;AAC9B,MAAM,cAAc,MAAW;AAExB,SAAS,YAAY;AAC1B,QAAM,MAAMC,cAAY,aAACC,SAAG;AAG5B,MAAI,IAAIC,iBAAK;AAGb,MAAI,UAAU,aAAa,QAAQ;AACnC,MAAI,UAAU,YAAY,OAAO;AACjC,MAAI,UAAU,YAAY,OAAO;AACjC,MAAI,UAAU,iBAAiB,WAAW;AAC1C,MAAI,UAAU,kBAAkB,YAAY;AAC5C,MAAI,UAAU,gBAAgB,UAAU;AACxC,MAAI,UAAU,iBAAiB,WAAW;AAG1C,MAAI,OAAO,iBAAiB,OAAOC,UAAG;AAEtC,SAAO;AAAA,IACL;AAAA,EACD;AACH;;;"}