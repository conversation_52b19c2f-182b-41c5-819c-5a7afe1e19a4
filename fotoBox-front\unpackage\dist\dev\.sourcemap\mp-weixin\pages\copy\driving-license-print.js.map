{"version": 3, "file": "driving-license-print.js", "sources": ["pages/copy/driving-license-print.vue", "C:/Program Files/HBuilderX.4.66.**********/plugins/uniapp-cli-vite/uniPage:/cGFnZXMvY29weS9kcml2aW5nLWxpY2Vuc2UtcHJpbnQudnVl"], "sourcesContent": ["<template>\n    <f-page-layout \n        title=\"驾驶证/行驶证拍照打印\" \n        fallback-path=\"/pages/copy/index\"\n        @back-click=\"onBackClick\"\n    >\n        <view class=\"driving-license-print\">\n            <!-- 顶部提示 -->\n            <view class=\"driving-license-print__header\">\n                <text class=\"driving-license-print__tip\">内容拍照时需要与背景颜色尽量区分</text>\n            </view>\n\n            <view class=\"driving-license-print__content\">\n                <!-- 驾驶证主页拍摄区域 -->\n                <view class=\"driving-license-print__card-container\" @click=\"showActionSheet('front')\">\n                    <view class=\"driving-license-print__card driving-license-print__card--front\">\n                        <!-- 驾驶证主页背景图片 -->\n                        <image\n                            v-if=\"frontCardImage\"\n                            class=\"driving-license-print__card-image\"\n                            :src=\"frontCardImage\"\n                            mode=\"aspectFill\"\n                        />\n                        <!-- 无图片时的占位符 -->\n                        <view v-else class=\"driving-license-print__card-placeholder\">\n                            <view class=\"driving-license-print__placeholder-text\">驾驶证主页模板</view>\n                            <view class=\"driving-license-print__placeholder-desc\">图片将在此处显示</view>\n                        </view>\n\n                        <!-- 相机图标覆盖层 -->\n                        <view class=\"driving-license-print__camera-overlay\">\n                            <view class=\"driving-license-print__camera-icon\">\n                                <uni-icons type=\"camera\" color=\"#FFFFFF\" size=\"40\"></uni-icons>\n                            </view>\n                            <view class=\"driving-license-print__camera-text\">\n                                拍摄主页\n                            </view>\n                        </view>\n                    </view>\n                </view>\n\n                <!-- 驾驶证副页拍摄区域 -->\n                <view class=\"driving-license-print__card-container\" @click=\"showActionSheet('back')\">\n                    <view class=\"driving-license-print__card driving-license-print__card--back\">\n                        <!-- 驾驶证副页背景图片 -->\n                        <image\n                            v-if=\"backCardImage\"\n                            class=\"driving-license-print__card-image\"\n                            :src=\"backCardImage\"\n                            mode=\"aspectFill\"\n                        />\n                        <!-- 无图片时的占位符 -->\n                        <view v-else class=\"driving-license-print__card-placeholder\">\n                            <view class=\"driving-license-print__placeholder-text\">驾驶证副页模板</view>\n                            <view class=\"driving-license-print__placeholder-desc\">图片将在此处显示</view>\n                        </view>\n\n                        <!-- 相机图标覆盖层 -->\n                        <view class=\"driving-license-print__camera-overlay\">\n                            <view class=\"driving-license-print__camera-icon\">\n                                <uni-icons type=\"camera\" color=\"#FFFFFF\" size=\"40\"></uni-icons>\n                            </view>\n                            <view class=\"driving-license-print__camera-text\">\n                                拍摄副页\n                            </view>\n                        </view>\n                    </view>\n                </view>\n            </view>\n\n        </view>\n    </f-page-layout>\n</template>\n\n<script>\nexport default {\n    data() {\n        return {\n            currentSide: '', // 'front' 或 'back'\n            frontPhoto: '', // 用户拍摄的主页照片\n            backPhoto: '', // 用户拍摄的副页照片\n            // 驾驶证模板图片 - 留空供后续引用\n            frontCardImage: '', // 主页模板图片路径\n            backCardImage: '' // 副页模板图片路径\n        };\n    },\n    methods: {\n        // 返回上一页\n        onBackClick() {\n            uni.navigateBack();\n        },\n        \n        // 显示操作选择\n        showActionSheet(side) {\n            this.currentSide = side;\n            const sideText = side === 'front' ? '主页' : '副页';\n\n            uni.showActionSheet({\n                itemList: ['拍照', '从相册选择'],\n                success: (res) => {\n                    if (res.tapIndex === 0) {\n                        this.takePhoto();\n                    } else if (res.tapIndex === 1) {\n                        this.chooseFromAlbum();\n                    }\n                }\n            });\n        },\n\n        // 拍照\n        takePhoto() {\n            uni.chooseImage({\n                count: 1,\n                sourceType: ['camera'],\n                success: (res) => {\n                    this.handleImageSelected(res.tempFilePaths[0]);\n                },\n                fail: (err) => {\n                    console.error('拍照失败:', err);\n                    uni.showToast({\n                        title: '拍照失败',\n                        icon: 'none'\n                    });\n                }\n            });\n        },\n\n        // 从相册选择\n        chooseFromAlbum() {\n            uni.chooseImage({\n                count: 1,\n                sourceType: ['album'],\n                success: (res) => {\n                    this.handleImageSelected(res.tempFilePaths[0]);\n                },\n                fail: (err) => {\n                    console.error('选择图片失败:', err);\n                    uni.showToast({\n                        title: '选择图片失败',\n                        icon: 'none'\n                    });\n                }\n            });\n        },\n\n        // 处理选中的图片\n        handleImageSelected(imagePath) {\n            if (this.currentSide === 'front') {\n                this.frontPhoto = imagePath;\n            } else {\n                this.backPhoto = imagePath;\n            }\n\n            const sideText = this.currentSide === 'front' ? '主页' : '副页';\n\n            // 检查是否两面都已拍摄\n            if (this.frontPhoto && this.backPhoto) {\n                this.proceedToPrintSettings();\n            } else {\n                uni.showToast({\n                    title: `${sideText}拍摄完成`,\n                    icon: 'success'\n                });\n            }\n        },\n\n        // 跳转到打印设置\n        proceedToPrintSettings() {\n            uni.showModal({\n                title: '拍摄完成',\n                content: '驾驶证/行驶证正反面已拍摄完成，是否进入打印设置？',\n                success: (res) => {\n                    if (res.confirm) {\n                        this.confirmPrint();\n                    }\n                }\n            });\n        },\n        \n        // 确认打印\n        confirmPrint() {\n            if (!this.frontPhoto || !this.backPhoto) {\n                uni.showToast({\n                    title: '请先拍摄主页和副页',\n                    icon: 'none'\n                });\n                return;\n            }\n\n            // 跳转到打印设置页面，传递照片数据\n            const photoData = encodeURIComponent(JSON.stringify({\n                front: this.frontPhoto,\n                back: this.backPhoto\n            }));\n            uni.navigateTo({\r\n\t\t\t\t\n            });\n        },\n\n        // 设置驾驶证模板图片 - 供后续引用\n        setDrivingLicenseTemplateImages(frontImageUrl, backImageUrl) {\n            this.frontCardImage = frontImageUrl;\n            this.backCardImage = backImageUrl;\n        }\n    },\n\n    // 页面加载时的处理\n    onLoad() {\n        // 这里可以设置默认的驾驶证模板图片\n        // 示例：this.setDrivingLicenseTemplateImages('/static/images/driving-license-front.png', '/static/images/driving-license-back.png');\n    }\n};\n</script>\n\n<style lang=\"scss\">\n.driving-license-print {\n    padding: 20rpx;\n\n    &__header {\n        background: linear-gradient(135deg, #FFE4B5 0%, #FFF8DC 100%);\n        padding: 20rpx;\n        border-radius: 12rpx;\n        margin-bottom: 30rpx;\n        text-align: center;\n    }\n\n    &__tip {\n        font-size: 28rpx;\n        color: #D2691E;\n        font-weight: 500;\n    }\n\n    &__content {\n        display: flex;\n        flex-direction: column;\n        gap: 30rpx;\n    }\n\n    &__card-container {\n        position: relative;\n\n        &:active {\n            opacity: 0.9;\n        }\n    }\n\n    &__card {\n        position: relative;\n        width: 100%;\n        height: 400rpx;\n        border-radius: 20rpx;\n        overflow: hidden;\n        box-shadow: 0 8rpx 24rpx rgba(0, 0, 0, 0.15);\n    }\n\n    &__card-image {\n        width: 100%;\n        height: 100%;\n        position: absolute;\n        top: 0;\n        left: 0;\n        border-radius: 20rpx;\n        object-fit: cover;\n    }\n\n    &__card-placeholder {\n        width: 100%;\n        height: 100%;\n        position: absolute;\n        top: 0;\n        left: 0;\n        border-radius: 20rpx;\n        background: linear-gradient(135deg, #F5F5F5 0%, #E8E8E8 100%);\n        display: flex;\n        flex-direction: column;\n        justify-content: center;\n        align-items: center;\n        border: 2rpx dashed #CCCCCC;\n        box-sizing: border-box;\n    }\n\n    &__placeholder-text {\n        font-size: 28rpx;\n        color: #999999;\n        font-weight: 500;\n        margin-bottom: 8rpx;\n    }\n\n    &__placeholder-desc {\n        font-size: 24rpx;\n        color: #BBBBBB;\n    }\n\n    // 相机覆盖层样式\n    &__camera-overlay {\n        position: absolute;\n        top: 0;\n        left: 0;\n        right: 0;\n        bottom: 0;\n        background: rgba(0, 0, 0, 0.4);\n        display: flex;\n        flex-direction: column;\n        justify-content: center;\n        align-items: center;\n        gap: 20rpx;\n        border-radius: 20rpx;\n    }\n\n    &__camera-icon {\n        width: 120rpx;\n        height: 120rpx;\n        background-color: rgba(0, 0, 0, 0.6);\n        border-radius: 50%;\n        display: flex;\n        align-items: center;\n        justify-content: center;\n    }\n\n    &__camera-text {\n        font-size: 32rpx;\n        color: #FFFFFF;\n        font-weight: 500;\n        text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.5);\n        text-align: center;\n    }\n}\n\n/* 适配不同屏幕尺寸 */\n@media screen and (max-width: 375px) {\n    .driving-license-print {\n        padding: 15rpx;\n\n        &__card {\n            height: 360rpx;\n        }\n\n        &__camera-icon {\n            width: 100rpx;\n            height: 100rpx;\n        }\n\n        &__camera-text {\n            font-size: 28rpx;\n        }\n\n        &__placeholder-text {\n            font-size: 24rpx;\n        }\n\n        &__placeholder-desc {\n            font-size: 20rpx;\n        }\n    }\n}\n</style>\n", "import MiniProgramPage from 'D:/Yunchuang/PhotoFront/fotoBox-front/pages/copy/driving-license-print.vue'\nwx.createPage(MiniProgramPage)"], "names": ["uni"], "mappings": ";;AA2EA,MAAK,YAAU;AAAA,EACX,OAAO;AACH,WAAO;AAAA,MACH,aAAa;AAAA;AAAA,MACb,YAAY;AAAA;AAAA,MACZ,WAAW;AAAA;AAAA;AAAA,MAEX,gBAAgB;AAAA;AAAA,MAChB,eAAe;AAAA;AAAA;EAEtB;AAAA,EACD,SAAS;AAAA;AAAA,IAEL,cAAc;AACVA,oBAAG,MAAC,aAAY;AAAA,IACnB;AAAA;AAAA,IAGD,gBAAgB,MAAM;AAClB,WAAK,cAAc;AAGnBA,oBAAAA,MAAI,gBAAgB;AAAA,QAChB,UAAU,CAAC,MAAM,OAAO;AAAA,QACxB,SAAS,CAAC,QAAQ;AACd,cAAI,IAAI,aAAa,GAAG;AACpB,iBAAK,UAAS;AAAA,qBACP,IAAI,aAAa,GAAG;AAC3B,iBAAK,gBAAe;AAAA,UACxB;AAAA,QACJ;AAAA,MACJ,CAAC;AAAA,IACJ;AAAA;AAAA,IAGD,YAAY;AACRA,oBAAAA,MAAI,YAAY;AAAA,QACZ,OAAO;AAAA,QACP,YAAY,CAAC,QAAQ;AAAA,QACrB,SAAS,CAAC,QAAQ;AACd,eAAK,oBAAoB,IAAI,cAAc,CAAC,CAAC;AAAA,QAChD;AAAA,QACD,MAAM,CAAC,QAAQ;AACXA,wBAAA,MAAA,MAAA,SAAA,+CAAc,SAAS,GAAG;AAC1BA,wBAAAA,MAAI,UAAU;AAAA,YACV,OAAO;AAAA,YACP,MAAM;AAAA,UACV,CAAC;AAAA,QACL;AAAA,MACJ,CAAC;AAAA,IACJ;AAAA;AAAA,IAGD,kBAAkB;AACdA,oBAAAA,MAAI,YAAY;AAAA,QACZ,OAAO;AAAA,QACP,YAAY,CAAC,OAAO;AAAA,QACpB,SAAS,CAAC,QAAQ;AACd,eAAK,oBAAoB,IAAI,cAAc,CAAC,CAAC;AAAA,QAChD;AAAA,QACD,MAAM,CAAC,QAAQ;AACXA,4FAAc,WAAW,GAAG;AAC5BA,wBAAAA,MAAI,UAAU;AAAA,YACV,OAAO;AAAA,YACP,MAAM;AAAA,UACV,CAAC;AAAA,QACL;AAAA,MACJ,CAAC;AAAA,IACJ;AAAA;AAAA,IAGD,oBAAoB,WAAW;AAC3B,UAAI,KAAK,gBAAgB,SAAS;AAC9B,aAAK,aAAa;AAAA,aACf;AACH,aAAK,YAAY;AAAA,MACrB;AAEA,YAAM,WAAW,KAAK,gBAAgB,UAAU,OAAO;AAGvD,UAAI,KAAK,cAAc,KAAK,WAAW;AACnC,aAAK,uBAAsB;AAAA,aACxB;AACHA,sBAAAA,MAAI,UAAU;AAAA,UACV,OAAO,GAAG,QAAQ;AAAA,UAClB,MAAM;AAAA,QACV,CAAC;AAAA,MACL;AAAA,IACH;AAAA;AAAA,IAGD,yBAAyB;AACrBA,oBAAAA,MAAI,UAAU;AAAA,QACV,OAAO;AAAA,QACP,SAAS;AAAA,QACT,SAAS,CAAC,QAAQ;AACd,cAAI,IAAI,SAAS;AACb,iBAAK,aAAY;AAAA,UACrB;AAAA,QACJ;AAAA,MACJ,CAAC;AAAA,IACJ;AAAA;AAAA,IAGD,eAAe;AACX,UAAI,CAAC,KAAK,cAAc,CAAC,KAAK,WAAW;AACrCA,sBAAAA,MAAI,UAAU;AAAA,UACV,OAAO;AAAA,UACP,MAAM;AAAA,QACV,CAAC;AACD;AAAA,MACJ;AAGkB,yBAAmB,KAAK,UAAU;AAAA,QAChD,OAAO,KAAK;AAAA,QACZ,MAAM,KAAK;AAAA,MACf,CAAC,CAAC;AACFA,oBAAAA,MAAI,WAAW,CAEf,CAAC;AAAA,IACJ;AAAA;AAAA,IAGD,gCAAgC,eAAe,cAAc;AACzD,WAAK,iBAAiB;AACtB,WAAK,gBAAgB;AAAA,IACzB;AAAA,EACH;AAAA;AAAA,EAGD,SAAS;AAAA,EAGT;AACJ;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AClNA,GAAG,WAAW,eAAe;"}