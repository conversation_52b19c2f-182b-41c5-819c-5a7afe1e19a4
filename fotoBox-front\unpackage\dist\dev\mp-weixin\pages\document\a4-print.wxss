/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.a4-print {
  background: linear-gradient(180deg, #FFE0B2 0%, #FFF8E1 100%);
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}
.a4-print__header {
  background-color: #FFD54F;
  padding: 20rpx;
  text-align: center;
}
.a4-print__tip {
  font-size: 28rpx;
  color: #8D6E63;
  font-weight: 500;
}
.a4-print__content {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 40rpx 20rpx;
}
.a4-print__title {
  font-size: 48rpx;
  font-weight: bold;
  color: #FF8F00;
  margin-bottom: 60rpx;
  text-align: center;
  text-shadow: 0 2rpx 4rpx rgba(255, 143, 0, 0.3);
}
.a4-print__preview {
  width: 100%;
  display: flex;
  justify-content: center;
  padding: 0 40rpx;
}
.a4-print__document {
  width: 100%;
  max-width: 600rpx;
  aspect-ratio: 1/1.414;
  background-color: #fff;
  border-radius: 16rpx;
  box-shadow: 0 8rpx 24rpx rgba(0, 0, 0, 0.12);
  padding: 50rpx 40rpx;
  display: flex;
  flex-direction: column;
  position: relative;
  overflow: hidden;
  border: 2rpx solid #FFE0B2;
}
.a4-print__line {
  height: 18rpx;
  background-color: #FFD54F;
  margin-bottom: 28rpx;
  border-radius: 9rpx;
  opacity: 0.8;
}
.a4-print__line:nth-child(1) {
  width: 95%;
}
.a4-print__line:nth-child(2) {
  width: 85%;
}
.a4-print__line:nth-child(3) {
  width: 70%;
}
.a4-print__line:nth-child(4) {
  width: 90%;
}
.a4-print__line:nth-child(5) {
  width: 100%;
}
.a4-print__line:nth-child(6) {
  width: 75%;
}
.a4-print__line:nth-child(7) {
  width: 100%;
}
.a4-print__line:nth-child(8) {
  width: 80%;
}
.a4-print__line:nth-child(9) {
  width: 65%;
}
.a4-print__line:nth-child(10) {
  width: 95%;
}
.a4-print__line:nth-child(11) {
  width: 85%;
}
.a4-print__line:nth-child(12) {
  width: 100%;
}
.a4-print__line:nth-child(13) {
  width: 70%;
}
.a4-print__line:nth-child(14) {
  width: 90%;
}
.a4-print__line:nth-child(15) {
  width: 60%;
}
.a4-print__camera {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 140rpx;
  height: 140rpx;
  background-color: rgba(0, 0, 0, 0.6);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.3);
}
.a4-print__text {
  position: absolute;
  bottom: 80rpx;
  left: 0;
  width: 100%;
  text-align: center;
  font-size: 36rpx;
  color: #757575;
  font-weight: 500;
}

/* 底部操作菜单 */
.action-sheet {
  position: fixed;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  z-index: 999;
}
.action-sheet__mask {
  position: absolute;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  background-color: rgba(0, 0, 0, 0.5);
}
.action-sheet__container {
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  background-color: #fff;
  border-top-left-radius: 24rpx;
  border-top-right-radius: 24rpx;
  overflow: hidden;
  transform: translateY(0);
  transition: transform 0.3s;
}
.action-sheet__item {
  height: 110rpx;
  line-height: 110rpx;
  text-align: center;
  font-size: 32rpx;
  color: #333;
  border-bottom: 1px solid #f5f5f5;
}
.action-sheet__item:active {
  background-color: #f9f9f9;
}
.action-sheet__cancel {
  height: 110rpx;
  line-height: 110rpx;
  text-align: center;
  font-size: 32rpx;
  color: #333;
  margin-top: 16rpx;
  background-color: #fff;
}
.action-sheet__cancel:active {
  background-color: #f9f9f9;
}