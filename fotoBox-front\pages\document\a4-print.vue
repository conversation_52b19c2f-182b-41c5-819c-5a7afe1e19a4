<template>
	<f-page-layout
		title="A4文档拍照打印"
		fallback-path="/pages/document/photo-print"
		@back-click="onBackClick"
	>
		<view class="a4-print">
			<view class="a4-print__header">
				<text class="a4-print__tip">内容拍照时需要与背景颜色尽量区分</text>
			</view>

			<view class="a4-print__content">
				<view class="a4-print__title">
					A4文档拍照打印
				</view>

				<!-- 文档预览区域 -->
				<view class="a4-print__preview" @click="showActionSheet">
					<view class="a4-print__document">
						<!-- 文档内容模拟线条 -->
						<view class="a4-print__line" v-for="(item, index) in 15" :key="index"></view>

						<!-- 相机图标 -->
						<view class="a4-print__camera">
							<uni-icons type="camera" color="#FFFFFF" size="32"></uni-icons>
						</view>

						<!-- 拍摄文档文本 -->
						<view class="a4-print__text">
							拍摄文档
						</view>
					</view>
				</view>
			</view>
		</view>
		
		<!-- 底部操作弹窗 -->
		<view class="action-sheet" v-if="showSheet" @click="hideActionSheet">
			<view class="action-sheet__mask"></view>
			<view class="action-sheet__container" @click.stop>
				<view class="action-sheet__item" @click="takePhoto">拍照</view>
				<view class="action-sheet__item" @click="chooseFromAlbum">相册</view>
				<view class="action-sheet__cancel" @click="hideActionSheet">取消</view>
			</view>
		</view>
	</f-page-layout>
</template>

<script>
	export default {
		data() {
			return {
				showSheet: false
			};
		},
		methods: {
			// 返回上一页
			onBackClick() {
				uni.navigateBack({
					delta: 1
				});
			},
			
			// 显示操作菜单
			showActionSheet() {
				this.showSheet = true;
			},
			
			// 隐藏操作菜单
			hideActionSheet() {
				this.showSheet = false;
			},
			
			// 拍摄文档
			takePhoto() {
				this.hideActionSheet();
				uni.chooseImage({
					count: 1,
					sourceType: ['camera'],
					success: (res) => {
						console.log('拍摄成功:', res.tempFilePaths);
						// 处理拍摄的图片
						this.previewPhoto(res.tempFilePaths[0]);
					},
					fail: () => {
						uni.showToast({
							title: '拍摄取消',
							icon: 'none'
						});
					}
				});
			},
			
			// 从相册选择
			chooseFromAlbum() {
				this.hideActionSheet();
				uni.chooseImage({
					count: 1,
					sourceType: ['album'],
					success: (res) => {
						console.log('选择成功:', res.tempFilePaths);
						// 处理选择的图片
						this.previewPhoto(res.tempFilePaths[0]);
					},
					fail: () => {
						uni.showToast({
							title: '选择取消',
							icon: 'none'
						});
					}
				});
			},
			
			// 预览照片
			previewPhoto(path) {
				// 这里可以添加预览和编辑功能
				uni.showToast({
					title: '文档拍摄成功',
					icon: 'success'
				});
			}
		}
	}
</script>

<style lang="scss">
	.a4-print {
		background: linear-gradient(180deg, #FFE0B2 0%, #FFF8E1 100%); // 渐变背景
		min-height: 100vh;
		display: flex;
		flex-direction: column;

		&__header {
			background-color: #FFD54F; // 更鲜艳的黄色
			padding: 20rpx;
			text-align: center;
		}

		&__tip {
			font-size: 28rpx;
			color: #8D6E63;
			font-weight: 500;
		}

		&__content {
			flex: 1;
			display: flex;
			flex-direction: column;
			align-items: center;
			padding: 40rpx 20rpx;
		}

		&__title {
			font-size: 48rpx;
			font-weight: bold;
			color: #FF8F00; // 更鲜艳的橙色
			margin-bottom: 60rpx;
			text-align: center;
			text-shadow: 0 2rpx 4rpx rgba(255, 143, 0, 0.3);
		}
		
		&__preview {
			width: 100%;
			display: flex;
			justify-content: center;
			padding: 0 40rpx;
		}

		&__document {
			width: 100%;
			max-width: 600rpx;
			aspect-ratio: 1 / 1.414; // A4纸张比例
			background-color: #fff;
			border-radius: 16rpx;
			box-shadow: 0 8rpx 24rpx rgba(0, 0, 0, 0.12);
			padding: 50rpx 40rpx;
			display: flex;
			flex-direction: column;
			position: relative;
			overflow: hidden;
			border: 2rpx solid #FFE0B2;
		}

		&__line {
			height: 18rpx;
			background-color: #FFD54F; // 更鲜艳的黄色线条
			margin-bottom: 28rpx;
			border-radius: 9rpx;
			opacity: 0.8;

			&:nth-child(1) { width: 95%; }
			&:nth-child(2) { width: 85%; }
			&:nth-child(3) { width: 70%; }
			&:nth-child(4) { width: 90%; }
			&:nth-child(5) { width: 100%; }
			&:nth-child(6) { width: 75%; }
			&:nth-child(7) { width: 100%; }
			&:nth-child(8) { width: 80%; }
			&:nth-child(9) { width: 65%; }
			&:nth-child(10) { width: 95%; }
			&:nth-child(11) { width: 85%; }
			&:nth-child(12) { width: 100%; }
			&:nth-child(13) { width: 70%; }
			&:nth-child(14) { width: 90%; }
			&:nth-child(15) { width: 60%; }
		}
		
		&__camera {
			position: absolute;
			top: 50%;
			left: 50%;
			transform: translate(-50%, -50%);
			width: 140rpx;
			height: 140rpx;
			background-color: rgba(0, 0, 0, 0.6);
			border-radius: 50%;
			display: flex;
			align-items: center;
			justify-content: center;
			box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.3);
		}

		&__text {
			position: absolute;
			bottom: 80rpx;
			left: 0;
			width: 100%;
			text-align: center;
			font-size: 36rpx;
			color: #757575;
			font-weight: 500;
		}
	}
	
	/* 底部操作菜单 */
	.action-sheet {
		position: fixed;
		top: 0;
		right: 0;
		bottom: 0;
		left: 0;
		z-index: 999;
		
		&__mask {
			position: absolute;
			top: 0;
			right: 0;
			bottom: 0;
			left: 0;
			background-color: rgba(0, 0, 0, 0.5);
		}
		
		&__container {
			position: absolute;
			bottom: 0;
			left: 0;
			width: 100%;
			background-color: #fff;
			border-top-left-radius: 24rpx;
			border-top-right-radius: 24rpx;
			overflow: hidden;
			transform: translateY(0);
			transition: transform 0.3s;
		}
		
		&__item {
			height: 110rpx;
			line-height: 110rpx;
			text-align: center;
			font-size: 32rpx;
			color: #333;
			border-bottom: 1px solid #f5f5f5;
			
			&:active {
				background-color: #f9f9f9;
			}
		}
		
		&__cancel {
			height: 110rpx;
			line-height: 110rpx;
			text-align: center;
			font-size: 32rpx;
			color: #333;
			margin-top: 16rpx;
			background-color: #fff;
			
			&:active {
				background-color: #f9f9f9;
			}
		}
	}
</style> 