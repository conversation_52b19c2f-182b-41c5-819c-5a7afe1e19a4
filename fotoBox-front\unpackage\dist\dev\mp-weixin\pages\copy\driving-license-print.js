"use strict";
const common_vendor = require("../../common/vendor.js");
const _sfc_main = {
  data() {
    return {
      currentSide: "",
      // 'front' 或 'back'
      frontPhoto: "",
      // 用户拍摄的主页照片
      backPhoto: "",
      // 用户拍摄的副页照片
      // 驾驶证模板图片 - 留空供后续引用
      frontCardImage: "",
      // 主页模板图片路径
      backCardImage: ""
      // 副页模板图片路径
    };
  },
  methods: {
    // 返回上一页
    onBackClick() {
      common_vendor.index.navigateBack();
    },
    // 显示操作选择
    showActionSheet(side) {
      this.currentSide = side;
      common_vendor.index.showActionSheet({
        itemList: ["拍照", "从相册选择"],
        success: (res) => {
          if (res.tapIndex === 0) {
            this.takePhoto();
          } else if (res.tapIndex === 1) {
            this.chooseFromAlbum();
          }
        }
      });
    },
    // 拍照
    takePhoto() {
      common_vendor.index.chooseImage({
        count: 1,
        sourceType: ["camera"],
        success: (res) => {
          this.handleImageSelected(res.tempFilePaths[0]);
        },
        fail: (err) => {
          common_vendor.index.__f__("error", "at pages/copy/driving-license-print.vue:119", "拍照失败:", err);
          common_vendor.index.showToast({
            title: "拍照失败",
            icon: "none"
          });
        }
      });
    },
    // 从相册选择
    chooseFromAlbum() {
      common_vendor.index.chooseImage({
        count: 1,
        sourceType: ["album"],
        success: (res) => {
          this.handleImageSelected(res.tempFilePaths[0]);
        },
        fail: (err) => {
          common_vendor.index.__f__("error", "at pages/copy/driving-license-print.vue:137", "选择图片失败:", err);
          common_vendor.index.showToast({
            title: "选择图片失败",
            icon: "none"
          });
        }
      });
    },
    // 处理选中的图片
    handleImageSelected(imagePath) {
      if (this.currentSide === "front") {
        this.frontPhoto = imagePath;
      } else {
        this.backPhoto = imagePath;
      }
      const sideText = this.currentSide === "front" ? "主页" : "副页";
      if (this.frontPhoto && this.backPhoto) {
        this.proceedToPrintSettings();
      } else {
        common_vendor.index.showToast({
          title: `${sideText}拍摄完成`,
          icon: "success"
        });
      }
    },
    // 跳转到打印设置
    proceedToPrintSettings() {
      common_vendor.index.showModal({
        title: "拍摄完成",
        content: "驾驶证/行驶证正反面已拍摄完成，是否进入打印设置？",
        success: (res) => {
          if (res.confirm) {
            this.confirmPrint();
          }
        }
      });
    },
    // 确认打印
    confirmPrint() {
      if (!this.frontPhoto || !this.backPhoto) {
        common_vendor.index.showToast({
          title: "请先拍摄主页和副页",
          icon: "none"
        });
        return;
      }
      encodeURIComponent(JSON.stringify({
        front: this.frontPhoto,
        back: this.backPhoto
      }));
      common_vendor.index.navigateTo({});
    },
    // 设置驾驶证模板图片 - 供后续引用
    setDrivingLicenseTemplateImages(frontImageUrl, backImageUrl) {
      this.frontCardImage = frontImageUrl;
      this.backCardImage = backImageUrl;
    }
  },
  // 页面加载时的处理
  onLoad() {
  }
};
if (!Array) {
  const _easycom_uni_icons2 = common_vendor.resolveComponent("uni-icons");
  const _component_f_page_layout = common_vendor.resolveComponent("f-page-layout");
  (_easycom_uni_icons2 + _component_f_page_layout)();
}
const _easycom_uni_icons = () => "../../uni_modules/uni-icons/components/uni-icons/uni-icons.js";
if (!Math) {
  _easycom_uni_icons();
}
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return common_vendor.e({
    a: $data.frontCardImage
  }, $data.frontCardImage ? {
    b: $data.frontCardImage
  } : {}, {
    c: common_vendor.p({
      type: "camera",
      color: "#FFFFFF",
      size: "40"
    }),
    d: common_vendor.o(($event) => $options.showActionSheet("front")),
    e: $data.backCardImage
  }, $data.backCardImage ? {
    f: $data.backCardImage
  } : {}, {
    g: common_vendor.p({
      type: "camera",
      color: "#FFFFFF",
      size: "40"
    }),
    h: common_vendor.o(($event) => $options.showActionSheet("back")),
    i: common_vendor.o($options.onBackClick),
    j: common_vendor.p({
      title: "驾驶证/行驶证拍照打印",
      ["fallback-path"]: "/pages/copy/index"
    })
  });
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages/copy/driving-license-print.js.map
