<template>
	<f-page-layout 
		title="照片打印" 
		fallback-path="/pages/index/index"
		@back-click="onBackClick"
	>
		<view class="photo">
			<!-- 一寸照片 -->
			<view class="photo__section">
				<view class="photo__card" @click="handleSelect('portrait')">
					<view class="photo__card-icon">
						<uni-icons type="image" color="#409EFF" size="24"></uni-icons>
					</view>
					<view class="photo__card-content">
						<view class="photo__card-title">6寸照片</view>
						<view class="photo__card-desc">高清打印、色彩饱满、质感细腻</view>
					</view>
				</view>
			</view>
			
			<!-- 照片套餐 -->
			<view class="photo__section">
				<view class="photo__card" @click="handleSelect('package')">
					<view class="photo__card-icon photo__card-icon--package">
						<uni-icons type="gift" color="#FF8C00" size="24"></uni-icons>
					</view>
					<view class="photo__card-content">
						<view class="photo__card-title">照片套餐</view>
						<view class="photo__card-desc">省钱套餐、价格实惠、品质如一</view>
					</view>
				</view>
			</view>
			
			<!-- 魔镜魔镜 -->
			<view class="photo__section">
				<view class="photo__card" @click="handleSelect('magic')">
					<view class="photo__card-icon photo__card-icon--magic">
						<uni-icons type="image" color="#7D7DFF" size="24"></uni-icons>
						<view class="photo__badge">新功能</view>
					</view>
					<view class="photo__card-content">
						<view class="photo__card-title">魔镜魔镜</view>
						<view class="photo__card-desc">智能生成形象照、写真照，体验全新的自己</view>
					</view>
				</view>
				
				<!-- 人像美颜和老照片修复 -->
				<view class="photo__sub-section">
					<view class="photo__sub-card" @click="handleSelect('beauty')">
						<view class="photo__sub-card-icon">
							<uni-icons type="camera" color="#7D7DFF" size="20"></uni-icons>
						</view>
						<view class="photo__sub-card-content">
							<view class="photo__sub-card-title">人像美颜</view>
							<view class="photo__sub-card-desc">祛痘、美白</view>
						</view>
					</view>
					
					<view class="photo__sub-card" @click="handleSelect('repair')">
						<view class="photo__sub-card-icon photo__sub-card-icon--repair">
							<uni-icons type="refresh" color="#7DFF7D" size="20"></uni-icons>
						</view>
						<view class="photo__sub-card-content">
							<view class="photo__sub-card-title">老照片修复</view>
							<view class="photo__sub-card-desc">还原、高清</view>
						</view>
					</view>
				</view>
			</view>
			
			<!-- 拼图 -->
			<view class="photo__section">
				<view class="photo__card" @click="handleSelect('collage')">
					<view class="photo__card-icon photo__card-icon--collage">
						<uni-icons type="images" color="#FF7D7D" size="24"></uni-icons>
					</view>
					<view class="photo__card-content">
						<view class="photo__card-title">拼图</view>
						<view class="photo__card-desc">聚会 旅行 纪念日 万能拼图模板 记录高光时刻</view>
					</view>
				</view>
				
				<!-- 拍立得风格和大头贴 -->
				<view class="photo__sub-section">
					<view class="photo__sub-card" @click="handleSelect('polaroid')">
						<view class="photo__sub-card-icon photo__sub-card-icon--polaroid">
							<uni-icons type="camera" color="#FF7D7D" size="20"></uni-icons>
						</view>
						<view class="photo__sub-card-content">
							<view class="photo__sub-card-title">拍立得风格</view>
							<view class="photo__sub-card-desc">款式经典 貌如成双</view>
						</view>
					</view>
					
					<view class="photo__sub-card" @click="handleSelect('sticker')">
						<view class="photo__sub-card-icon photo__sub-card-icon--sticker">
							<uni-icons type="paperclip" color="#409EFF" size="20"></uni-icons>
						</view>
						<view class="photo__sub-card-content">
							<view class="photo__sub-card-title">大头贴</view>
							<view class="photo__sub-card-desc">记录甜蜜时刻</view>
						</view>
					</view>
				</view>
			</view>
			
			<!-- 小尺寸照片 -->
			<view class="photo__row">
				<view class="photo__half-card" @click="handleSelect('inch')">
					<view class="photo__half-card-icon">
						<uni-icons type="person" color="#FF8C00" size="20"></uni-icons>
					</view>
					<view class="photo__half-card-content">
						<view class="photo__half-card-title">寸照</view>
						<view class="photo__half-card-desc">1寸、2寸各尺寸</view>
					</view>
				</view>
				
				<view class="photo__half-card" @click="handleSelect('small')">
					<view class="photo__half-card-icon photo__half-card-icon--small">
						<uni-icons type="image" color="#7D7DFF" size="20"></uni-icons>
					</view>
					<view class="photo__half-card-content">
						<view class="photo__half-card-title">5寸照片</view>
						<view class="photo__half-card-desc">小巧易分享</view>
					</view>
				</view>
			</view>
			
			<!-- 其他尺寸 -->
			<view class="photo__row">
				<view class="photo__half-card" @click="handleSelect('a4')">
					<view class="photo__half-card-icon photo__half-card-icon--a4">
						<uni-icons type="paperclip" color="#FF7D7D" size="20"></uni-icons>
					</view>
					<view class="photo__half-card-content">
						<view class="photo__half-card-title">A4图文</view>
						<view class="photo__half-card-desc">图片打在A4纸</view>
					</view>
				</view>
				
				<view class="photo__half-card" @click="handleSelect('calendar')">
					<view class="photo__half-card-icon photo__half-card-icon--calendar">
						<uni-icons type="calendar" color="#7DFF7D" size="20"></uni-icons>
					</view>
					<view class="photo__half-card-content">
						<view class="photo__half-card-title">台历相册</view>
						<view class="photo__half-card-desc">个性专属台历</view>
					</view>
				</view>
			</view>
			
			<!-- 页脚尺寸说明 -->
			<view class="photo__footer">
				<text class="photo__footer-text">
					相纸尺寸：6寸照片/5寸照片/拼图(15.2x10.2cm)、8寸照片(20.3x15.2cm)、台历相册(16x17.5cm)
				</text>
			</view>
		</view>
	</f-page-layout>
</template>

<script>
	export default {
		methods: {
			// 返回上一页
			onBackClick() {
				uni.navigateBack({
					delta: 1
				});
			},
			// 处理选择
			handleSelect(type) {
				console.log('选择照片类型:', type);
				
				// 处理不同的照片打印类型
				switch(type) {
					case 'portrait':
						// 跳转到6寸照片页面
						uni.navigateTo({
							url: '/pages/photo/six-inch-photo'
						});
						break;
					case 'package':
						uni.showToast({
							title: '选择了照片套餐',
							icon: 'none'
						});
						break;
					case 'magic':
						uni.showToast({
							title: '选择了魔镜魔镜',
							icon: 'none'
						});
						break;
					case 'beauty':
						uni.showToast({
							title: '选择了人像美颜',
							icon: 'none'
						});
						break;
					case 'repair':
						uni.showToast({
							title: '选择了老照片修复',
							icon: 'none'
						});
						break;
					case 'collage':
						uni.showToast({
							title: '选择了拼图',
							icon: 'none'
						});
						break;
					case 'polaroid':
						uni.showToast({
							title: '选择了拍立得风格',
							icon: 'none'
						});
						break;
					case 'sticker':
						uni.showToast({
							title: '选择了大头贴',
							icon: 'none'
						});
						break;
					case 'inch':
						uni.navigateTo({
							url: '/pages/id-photo/index'
						});
						break;
					case 'small':
						// 跳转到5寸照片页面
						uni.navigateTo({
							url: '/pages/photo/five-inch-photo'
						});
						break;
					case 'a4':
						// 跳转到A4图文页面
						uni.navigateTo({
							url: '/pages/photo/a4-photo'
						});
						break;
					case 'calendar':
						uni.showToast({
							title: '选择了台历相册',
							icon: 'none'
						});
						break;
					default:
						break;
				}
			}
		}
	}
</script>

<style lang="scss">
	.photo {
		padding: 16rpx;
		display: flex;
		flex-direction: column;
		gap: 20rpx;
		
		&__section {
			background-color: #fff;
			border-radius: 16rpx;
			overflow: hidden;
			box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.03);
		}
		
		&__card {
			display: flex;
			padding: 30rpx;
			align-items: center;
			
			&:active {
				background-color: #f9f9f9;
			}
		}
		
		&__card-icon {
			width: 80rpx;
			height: 80rpx;
			border-radius: 16rpx;
			overflow: hidden;
			display: flex;
			justify-content: center;
			align-items: center;
			margin-right: 30rpx;
			position: relative;
			background-color: #f0f9ff;
			
			&--package {
				background-color: #fff8f0;
			}
			
			&--magic {
				background-color: #f0f0ff;
			}
			
			&--collage {
				background-color: #fff0f0;
			}
		}
		
		&__badge {
			position: absolute;
			top: -8rpx;
			right: -8rpx;
			background-color: #FF5252;
			color: #fff;
			font-size: 16rpx;
			padding: 4rpx 8rpx;
			border-radius: 8rpx;
			transform: scale(0.8);
			white-space: nowrap;
		}
		
		&__card-image {
			width: 80%;
			height: 80%;
		}
		
		&__card-content {
			flex: 1;
		}
		
		&__card-title {
			font-size: 36rpx;
			font-weight: 500;
			color: #333;
			margin-bottom: 8rpx;
		}
		
		&__card-desc {
			font-size: 24rpx;
			color: #999;
			line-height: 1.2;
		}
		
		&__sub-section {
			display: flex;
			border-top: 1px solid #f5f5f5;
		}
		
		&__sub-card {
			flex: 1;
			padding: 24rpx;
			display: flex;
			align-items: center;
			
			&:first-child {
				border-right: 1px solid #f5f5f5;
			}
			
			&:active {
				background-color: #f9f9f9;
			}
		}
		
		&__sub-card-icon {
			width: 60rpx;
			height: 60rpx;
			border-radius: 12rpx;
			background-color: #f0f0ff;
			display: flex;
			justify-content: center;
			align-items: center;
			margin-right: 20rpx;
			
			&--repair {
				background-color: #f0fff0;
			}
			
			&--polaroid {
				background-color: #fff0f0;
			}
			
			&--sticker {
				background-color: #f0f9ff;
			}
		}
		
		&__sub-card-content {
			flex: 1;
		}
		
		&__sub-card-title {
			font-size: 28rpx;
			font-weight: 500;
			color: #333;
			margin-bottom: 4rpx;
		}
		
		&__sub-card-desc {
			font-size: 22rpx;
			color: #999;
			line-height: 1.2;
		}
		
		&__row {
			display: flex;
			gap: 20rpx;
		}
		
		&__half-card {
			flex: 1;
			background-color: #fff;
			border-radius: 16rpx;
			padding: 24rpx;
			display: flex;
			align-items: center;
			box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.03);
			
			&:active {
				background-color: #f9f9f9;
			}
		}
		
		&__half-card-icon {
			width: 60rpx;
			height: 60rpx;
			border-radius: 12rpx;
			background-color: #fff8f0;
			display: flex;
			justify-content: center;
			align-items: center;
			margin-right: 20rpx;
			
			&--small {
				background-color: #f0f0ff;
			}
			
			&--a4 {
				background-color: #fff0f0;
			}
			
			&--calendar {
				background-color: #f0fff0;
			}
		}
		
		&__half-card-content {
			flex: 1;
		}
		
		&__half-card-title {
			font-size: 28rpx;
			font-weight: 500;
			color: #333;
			margin-bottom: 4rpx;
		}
		
		&__half-card-desc {
			font-size: 22rpx;
			color: #999;
			line-height: 1.2;
		}
		
		&__footer {
			margin-top: 10rpx;
			padding: 20rpx 10rpx;
		}
		
		&__footer-text {
			font-size: 22rpx;
			color: #999;
			text-align: center;
			display: block;
		}
	}
</style> 