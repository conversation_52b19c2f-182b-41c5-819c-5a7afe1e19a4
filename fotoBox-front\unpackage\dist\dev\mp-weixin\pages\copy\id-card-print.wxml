<f-page-layout wx:if="{{j}}" u-s="{{['d']}}" bindbackClick="{{i}}" u-i="42c63099-0" bind:__l="__l" u-p="{{j}}"><view class="id-card-print"><view class="id-card-print__header"><text class="id-card-print__tip">内容拍照时需要与背景颜色尽量区分</text></view><view class="id-card-print__content"><view class="id-card-print__title"> 身份证/居住证拍照打印 </view><view class="id-card-print__preview" bindtap="{{d}}"><view class="id-card-print__card id-card-print__card--front"><image wx:if="{{a}}" class="id-card-print__card-image" src="{{b}}" mode="aspectFill"/><view wx:else class="id-card-print__card-placeholder"><view class="id-card-print__placeholder-text">身份证正面模板</view><view class="id-card-print__placeholder-desc">图片将在此处显示</view></view><view class="id-card-print__camera-overlay"><view class="id-card-print__camera"><uni-icons wx:if="{{c}}" u-i="42c63099-1,42c63099-0" bind:__l="__l" u-p="{{c}}"></uni-icons></view><view class="id-card-print__text"> 拍摄正面 </view></view></view></view><view class="id-card-print__preview" bindtap="{{h}}"><view class="id-card-print__card id-card-print__card--back"><image wx:if="{{e}}" class="id-card-print__card-image" src="{{f}}" mode="aspectFill"/><view wx:else class="id-card-print__card-placeholder"><view class="id-card-print__placeholder-text">身份证背面模板</view><view class="id-card-print__placeholder-desc">图片将在此处显示</view></view><view class="id-card-print__camera-overlay"><view class="id-card-print__camera"><uni-icons wx:if="{{g}}" u-i="42c63099-2,42c63099-0" bind:__l="__l" u-p="{{g}}"></uni-icons></view><view class="id-card-print__text"> 拍摄背面 </view></view></view></view></view></view></f-page-layout>