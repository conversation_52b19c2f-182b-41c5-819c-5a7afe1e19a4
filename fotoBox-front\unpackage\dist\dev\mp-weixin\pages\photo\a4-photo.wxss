/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.a4-photo.data-v-c9393d14 {
  background-color: #f5f5f5;
  min-height: 100vh;
  padding-bottom: 200rpx;
}
.a4-photo .empty-state.data-v-c9393d14 {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 200rpx 40rpx;
}
.a4-photo .empty-state .empty-box.data-v-c9393d14 {
  width: 200rpx;
  height: 200rpx;
  margin-bottom: 40rpx;
}
.a4-photo .empty-state .empty-text.data-v-c9393d14 {
  font-size: 28rpx;
  color: #999999;
}
.a4-photo .file-list-container.data-v-c9393d14 {
  height: calc(100vh - 200rpx);
}
.a4-photo .file-list-container .file-list-scroll.data-v-c9393d14 {
  height: 100%;
}
.a4-photo .file-list-container .file-list-scroll .file-list.data-v-c9393d14 {
  padding: 20rpx;
}
.a4-photo .file-list-container .file-list-scroll .file-list .file-item.data-v-c9393d14 {
  margin-bottom: 20rpx;
}
.a4-photo .file-list-container .file-list-scroll .file-list .file-item .file-card.data-v-c9393d14 {
  background-color: #ffffff;
  border-radius: 12rpx;
  padding: 30rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
}
.a4-photo .file-list-container .file-list-scroll .file-list .file-item .file-card.uploading .file-name.data-v-c9393d14 {
  font-size: 28rpx;
  color: #333;
  font-weight: 500;
  margin-bottom: 20rpx;
}
.a4-photo .file-list-container .file-list-scroll .file-list .file-item .file-card.uploading .upload-progress-text.data-v-c9393d14 {
  font-size: 24rpx;
  color: #666;
  margin-bottom: 10rpx;
}
.a4-photo .file-list-container .file-list-scroll .file-list .file-item .file-card.uploading .upload-progress-bar-container.data-v-c9393d14 {
  width: 100%;
  height: 8rpx;
  background-color: #f0f0f0;
  border-radius: 4rpx;
  margin-bottom: 10rpx;
  overflow: hidden;
}
.a4-photo .file-list-container .file-list-scroll .file-list .file-item .file-card.uploading .upload-progress-bar-container .upload-progress-bar.data-v-c9393d14 {
  height: 100%;
  background-color: #007aff;
  border-radius: 4rpx;
  transition: width 0.3s ease;
}
.a4-photo .file-list-container .file-list-scroll .file-list .file-item .file-card.uploading .upload-progress-value.data-v-c9393d14 {
  font-size: 24rpx;
  color: #007aff;
  text-align: right;
  margin-bottom: 20rpx;
}
.a4-photo .file-list-container .file-list-scroll .file-list .file-item .file-card.uploading .delete-btn.data-v-c9393d14 {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  width: 100rpx;
  height: 80rpx;
  margin-left: auto;
}
.a4-photo .file-list-container .file-list-scroll .file-list .file-item .file-card.uploading .delete-btn text.data-v-c9393d14 {
  font-size: 24rpx;
  color: #333;
  margin-top: 4rpx;
}
.a4-photo .file-list-container .file-list-scroll .file-list .file-item .file-card.print-settings .file-header.data-v-c9393d14 {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-bottom: 20rpx;
  border-bottom: 1px solid #f0f0f0;
}
.a4-photo .file-list-container .file-list-scroll .file-list .file-item .file-card.print-settings .file-header .file-name.data-v-c9393d14 {
  font-size: 28rpx;
  color: #333;
  font-weight: 500;
  max-width: 70%;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}
.a4-photo .file-list-container .file-list-scroll .file-list .file-item .file-card.print-settings .file-header .photo-thumbnail.data-v-c9393d14 {
  width: 80rpx;
  height: 80rpx;
  border-radius: 6rpx;
  object-fit: cover;
}
.a4-photo .file-list-container .file-list-scroll .file-list .file-item .file-card.print-settings .setting-item.data-v-c9393d14 {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20rpx 0;
  border-bottom: 1px solid #f0f0f0;
}
.a4-photo .file-list-container .file-list-scroll .file-list .file-item .file-card.print-settings .setting-item .setting-label.data-v-c9393d14 {
  font-size: 28rpx;
  color: #333;
}
.a4-photo .file-list-container .file-list-scroll .file-list .file-item .file-card.print-settings .setting-item .copies-selector.data-v-c9393d14 {
  display: flex;
  align-items: center;
  background-color: #f5f5f5;
  border-radius: 6rpx;
}
.a4-photo .file-list-container .file-list-scroll .file-list .file-item .file-card.print-settings .setting-item .copies-selector .copies-btn.data-v-c9393d14 {
  width: 60rpx;
  height: 60rpx;
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 32rpx;
  color: #333;
  background-color: #f0f0f0;
}
.a4-photo .file-list-container .file-list-scroll .file-list .file-item .file-card.print-settings .setting-item .copies-selector .copies-input.data-v-c9393d14 {
  width: 80rpx;
  height: 60rpx;
  text-align: center;
  font-size: 28rpx;
  color: #333;
  background-color: #ffffff;
}
.a4-photo .file-list-container .file-list-scroll .file-list .file-item .file-card.print-settings .setting-item .option-selector.data-v-c9393d14 {
  display: flex;
  gap: 10rpx;
}
.a4-photo .file-list-container .file-list-scroll .file-list .file-item .file-card.print-settings .setting-item .option-btn.data-v-c9393d14 {
  width: 100rpx;
  height: 70rpx;
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: #f5f5f5;
  color: #999;
  border-radius: 6rpx;
  font-size: 28rpx;
}
.a4-photo .file-list-container .file-list-scroll .file-list .file-item .file-card.print-settings .setting-item .option-btn.active.data-v-c9393d14 {
  background-color: #FFA500;
  color: #ffffff;
}
.a4-photo .file-list-container .file-list-scroll .file-list .file-item .file-card.print-settings .file-actions.data-v-c9393d14 {
  display: flex;
  justify-content: flex-end;
  padding-top: 20rpx;
}
.a4-photo .file-list-container .file-list-scroll .file-list .file-item .file-card.print-settings .file-actions .action-btn.data-v-c9393d14 {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-left: 40rpx;
}
.a4-photo .file-list-container .file-list-scroll .file-list .file-item .file-card.print-settings .file-actions .action-btn text.data-v-c9393d14 {
  font-size: 24rpx;
  color: #333;
  margin-top: 4rpx;
}
.a4-photo .fixed-bottom-area.data-v-c9393d14 {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  width: 100%;
  background-color: #f8f8f8;
  box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.05);
  z-index: 10;
  padding-bottom: env(safe-area-inset-bottom);
}
.a4-photo .fixed-bottom-area .notice-text.data-v-c9393d14 {
  font-size: 24rpx;
  color: #ff5a5f;
  text-align: center;
  padding: 20rpx;
  line-height: 1.5;
}
.a4-photo .fixed-bottom-area .btn-group.data-v-c9393d14 {
  display: flex;
  justify-content: space-between;
  padding: 0 30rpx 30rpx;
}
.a4-photo .fixed-bottom-area .btn-group .select-btn.data-v-c9393d14 {
  flex: 1;
  height: 80rpx;
  line-height: 80rpx;
  background-color: #ffffff;
  color: #FFA500;
  border: 1px solid #FFA500;
  border-radius: 40rpx;
  font-size: 28rpx;
  margin-right: 20rpx;
}
.a4-photo .fixed-bottom-area .btn-group .print-btn.data-v-c9393d14 {
  flex: 1;
  height: 80rpx;
  line-height: 80rpx;
  background-color: #CCCCCC;
  color: #ffffff;
  border-radius: 40rpx;
  font-size: 28rpx;
}
.a4-photo .fixed-bottom-area .btn-group .print-btn.data-v-c9393d14:not([disabled]) {
  background-color: #007aff;
}