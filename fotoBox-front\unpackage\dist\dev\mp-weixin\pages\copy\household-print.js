"use strict";
const common_vendor = require("../../common/vendor.js");
const _sfc_main = {
  data() {
    return {
      currentPage: "",
      // 当前拍摄的页面 'master' 或 'personal'
      photos: {
        master: "",
        // 户主页拍摄照片
        personal: ""
        // 个人信息页拍摄照片
      },
      // 户口本模板图片 - 留空供后续引用
      masterPageImage: "",
      // 户主页模板图片路径
      personalPageImage: ""
      // 个人信息页模板图片路径
    };
  },
  computed: {
    // 检查是否可以打印（两张照片都已拍摄）
    canPrint() {
      return this.photos.master && this.photos.personal;
    }
  },
  methods: {
    // 返回上一页
    onBackClick() {
      common_vendor.index.navigateBack();
    },
    // 显示操作选择
    showActionSheet(page) {
      this.currentPage = page;
      common_vendor.index.showActionSheet({
        itemList: ["拍照", "从相册选择"],
        success: (res) => {
          if (res.tapIndex === 0) {
            this.takePhoto();
          } else if (res.tapIndex === 1) {
            this.chooseFromAlbum();
          }
        }
      });
    },
    // 拍照
    takePhoto() {
      common_vendor.index.chooseImage({
        count: 1,
        sourceType: ["camera"],
        success: (res) => {
          this.handleImageSelected(res.tempFilePaths[0]);
        },
        fail: (err) => {
          common_vendor.index.__f__("error", "at pages/copy/household-print.vue:138", "拍照失败:", err);
          common_vendor.index.showToast({
            title: "拍照失败",
            icon: "none"
          });
        }
      });
    },
    // 从相册选择
    chooseFromAlbum() {
      common_vendor.index.chooseImage({
        count: 1,
        sourceType: ["album"],
        success: (res) => {
          this.handleImageSelected(res.tempFilePaths[0]);
        },
        fail: (err) => {
          common_vendor.index.__f__("error", "at pages/copy/household-print.vue:156", "选择图片失败:", err);
          common_vendor.index.showToast({
            title: "选择图片失败",
            icon: "none"
          });
        }
      });
    },
    // 处理选择的图片
    handleImageSelected(imagePath) {
      this.photos[this.currentPage] = imagePath;
      const pageText = this.currentPage === "master" ? "户主页" : "个人信息页";
      common_vendor.index.showToast({
        title: `${pageText}拍摄完成`,
        icon: "success"
      });
      if (this.canPrint) {
        setTimeout(() => {
          this.proceedToPrintSettings();
        }, 1500);
      }
    },
    // 重新拍照
    resetPhotos() {
      common_vendor.index.showModal({
        title: "确认重新拍照",
        content: "是否要清除所有已拍摄的照片？",
        success: (res) => {
          if (res.confirm) {
            this.photos = {
              master: "",
              personal: ""
            };
            common_vendor.index.showToast({
              title: "已清除照片",
              icon: "success"
            });
          }
        }
      });
    },
    // 跳转到打印设置
    proceedToPrintSettings() {
      common_vendor.index.showModal({
        title: "拍摄完成",
        content: "户口本两页已拍摄完成，是否进入打印设置？",
        success: (res) => {
          if (res.confirm) {
            this.confirmPrint();
          }
        }
      });
    },
    // 确认打印
    confirmPrint() {
      if (!this.canPrint) {
        common_vendor.index.showToast({
          title: "请先拍摄户主页和个人信息页",
          icon: "none"
        });
        return;
      }
      encodeURIComponent(JSON.stringify(this.photos));
      common_vendor.index.navigateTo({});
    },
    // 设置户口本模板图片 - 供后续引用
    setHouseholdTemplateImages(masterImageUrl, personalImageUrl) {
      this.masterPageImage = masterImageUrl;
      this.personalPageImage = personalImageUrl;
    }
  },
  // 页面加载时的处理
  onLoad() {
  }
};
if (!Array) {
  const _easycom_uni_icons2 = common_vendor.resolveComponent("uni-icons");
  const _component_f_page_layout = common_vendor.resolveComponent("f-page-layout");
  (_easycom_uni_icons2 + _component_f_page_layout)();
}
const _easycom_uni_icons = () => "../../uni_modules/uni-icons/components/uni-icons/uni-icons.js";
if (!Math) {
  _easycom_uni_icons();
}
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return common_vendor.e({
    a: $data.masterPageImage
  }, $data.masterPageImage ? {
    b: $data.masterPageImage
  } : {}, {
    c: common_vendor.p({
      type: "camera",
      color: "#FFFFFF",
      size: "30"
    }),
    d: common_vendor.o(($event) => $options.showActionSheet("master")),
    e: $data.personalPageImage
  }, $data.personalPageImage ? {
    f: $data.personalPageImage
  } : {}, {
    g: common_vendor.p({
      type: "camera",
      color: "#FFFFFF",
      size: "30"
    }),
    h: common_vendor.o(($event) => $options.showActionSheet("personal")),
    i: common_vendor.o((...args) => $options.resetPhotos && $options.resetPhotos(...args)),
    j: common_vendor.o((...args) => $options.confirmPrint && $options.confirmPrint(...args)),
    k: !$options.canPrint,
    l: common_vendor.o($options.onBackClick),
    m: common_vendor.p({
      title: "户口本打印",
      ["fallback-path"]: "/pages/copy/index"
    })
  });
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages/copy/household-print.js.map
