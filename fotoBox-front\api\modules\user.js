/**
 * 用户相关API
 */
import request from '../request';

/**
 * 微信小程序登录
 * @param {Object} data - 登录参数
 * @param {String} data.code - 微信登录code
 */
export function wxLogin(data) {
  return request({
    url: '/client/wxapp/user/login_or_register',
    method: 'POST',
    data
  });
}

/**
 * 退出登录
 */
export function logout() {
  return request({
    url: '/client/wxapp/user/logout',
    method: 'POST'
  });
}

/**
 * 获取用户信息
 */
export function getUserInfo() {
  return request({
    url: '/client/wxapp/user/info',
    method: 'GET'
  });
}

/**
 * 获取用户资产信息
 */
export function getUserAssets() {
  return request({
    url: '/client/wxapp/user/assets',
    method: 'GET'
  });
}

/**
 * 更新用户信息
 * @param {Object} data - 用户信息
 */
export function updateUserInfo(data) {
  return request({
    url: '/client/wxapp/user/update',
    method: 'POST',
    data
  });
}
