<template>
    <f-page-layout 
        title="营业执照拍照打印" 
        fallback-path="/pages/copy/index"
        @back-click="onBackClick"
    >
        <view class="business-license-print">
            <!-- 顶部提示 -->
            <view class="business-license-print__header">
                <text class="business-license-print__tip">内容拍照时需要与背景颜色尽量区分</text>
            </view>

            <!-- 营业执照表格区域 -->
            <view class="business-license-print__license-container" @click="showActionSheet">
                <!-- 页面标题 -->
                <view class="business-license-print__title">
                    <text class="business-license-print__title-text">营业执照拍照打印</text>
                </view>

                <!-- 表格行 -->
                <view class="business-license-print__table">
                    <view class="business-license-print__row" v-for="(row, index) in tableRows" :key="index">
                        <view class="business-license-print__cell" v-for="(cell, cellIndex) in row" :key="cellIndex">
                            <text class="business-license-print__cell-text">{{ cell }}</text>
                        </view>
                    </view>
                </view>

                <!-- 中央拍照区域 -->
                <view class="business-license-print__photo-area">
                    <view class="business-license-print__camera-circle">
                        <uni-icons type="camera" color="#FFFFFF" size="40"></uni-icons>
                    </view>
                    <text class="business-license-print__photo-text">拍摄营业执照</text>
                </view>
            </view>
        </view>
    </f-page-layout>
</template>

<script>
export default {
    data() {
        return {
            businessLicensePhoto: '', // 用户拍摄的营业执照照片
            // 营业执照模板图片 - 留空供后续引用
            businessLicenseTemplateImage: '', // 营业执照模板图片路径
            // 表格行数据（模拟营业执照表格结构）
            tableRows: [
                ['', '', '', ''],
                ['', '', '', ''],
                ['', '', '', ''],
                ['', '', '', ''],
                ['', '', '', ''],
                ['', '', '', ''],
                ['', '', '', ''],
                ['', '', '', ''],
                ['', '', '', ''],
                ['', '', '', ''],
                ['', '', '', ''],
                ['', '', '', '']
            ]
        };
    },
    methods: {
        // 返回上一页
        onBackClick() {
            uni.navigateBack();
        },
        
        // 显示操作选择
        showActionSheet() {
            uni.showActionSheet({
                itemList: ['拍照', '从相册选择'],
                success: (res) => {
                    if (res.tapIndex === 0) {
                        this.takePhoto();
                    } else if (res.tapIndex === 1) {
                        this.chooseFromAlbum();
                    }
                }
            });
        },

        // 拍照功能
        takePhoto() {
            uni.chooseImage({
                count: 1,
                sourceType: ['camera'],
                success: (res) => {
                    this.handleImageSelected(res.tempFilePaths[0]);
                },
                fail: (err) => {
                    console.error('拍照失败:', err);
                    uni.showToast({
                        title: '拍照失败',
                        icon: 'none'
                    });
                }
            });
        },

        // 从相册选择
        chooseFromAlbum() {
            uni.chooseImage({
                count: 1,
                sourceType: ['album'],
                success: (res) => {
                    this.handleImageSelected(res.tempFilePaths[0]);
                },
                fail: (err) => {
                    console.error('选择图片失败:', err);
                    uni.showToast({
                        title: '选择图片失败',
                        icon: 'none'
                    });
                }
            });
        },

        // 处理选择的图片
        handleImageSelected(imagePath) {
            this.businessLicensePhoto = imagePath;
            uni.showToast({
                title: '营业执照拍摄完成',
                icon: 'success'
            });

            // 自动提示进入打印设置
            setTimeout(() => {
                this.proceedToPrintSettings();
            }, 1500);
        },
        
        // 跳转到打印设置
        proceedToPrintSettings() {
            uni.showModal({
                title: '拍摄完成',
                content: '营业执照已拍摄完成，是否进入打印设置？',
                success: (res) => {
                    if (res.confirm) {
                        this.confirmPrint();
                    }
                }
            });
        },

        // 确认打印
        confirmPrint() {
            if (!this.businessLicensePhoto) {
                uni.showToast({
                    title: '请先拍摄营业执照',
                    icon: 'none'
                });
                return;
            }

            // 跳转到打印设置页面，传递照片数据
            const photoData = encodeURIComponent(JSON.stringify({
                businessLicense: this.businessLicensePhoto
            }));
            uni.navigateTo({

            });
        },

        // 设置营业执照模板图片 - 供后续引用
        setBusinessLicenseTemplateImage(templateImageUrl) {
            this.businessLicenseTemplateImage = templateImageUrl;
        }
    },

    // 页面加载时的处理
    onLoad() {
        // 这里可以设置默认的营业执照模板图片
        // 示例：this.setBusinessLicenseTemplateImage('/static/images/business-license-template.png');
    }
};
</script>

<style lang="scss">
.business-license-print {
    background-color: #f5f5f5;
    min-height: 100vh;

    &__header {
        background: linear-gradient(135deg, #FFE4B5 0%, #FFF8DC 100%);
        padding: 20rpx;
        text-align: center;
        margin: 0;
    }

    &__tip {
        font-size: 28rpx;
        color: #D2691E;
        font-weight: 500;
    }

    &__license-container {
        background-color: #F5E6A8;
        margin: 20rpx;
        border-radius: 16rpx;
        padding: 40rpx 30rpx;
        position: relative;
        min-height: 800rpx;
        box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);

        &:active {
            opacity: 0.9;
        }
    }

    &__title {
        text-align: center;
        margin-bottom: 50rpx;
    }

    &__title-text {
        font-size: 48rpx;
        font-weight: bold;
        color: #FFA500;
        text-shadow: 2rpx 2rpx 4rpx rgba(0, 0, 0, 0.1);
    }

    &__table {
        margin-bottom: 40rpx;
        width: 100%;
    }

    &__row {
        display: flex;
        margin-bottom: 12rpx;

        &:last-child {
            margin-bottom: 0;
        }
    }

    &__cell {
        flex: 1;
        height: 60rpx;
        background-color: #FFF8DC;
        border: 2rpx solid #E6D35A;
        margin-right: 12rpx;
        border-radius: 6rpx;
        display: flex;
        align-items: center;
        justify-content: center;

        &:last-child {
            margin-right: 0;
        }
    }

    &__cell-text {
        font-size: 24rpx;
        color: transparent; // 隐藏文字，只显示表格结构
    }

    &__photo-area {
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        display: flex;
        flex-direction: column;
        align-items: center;
        gap: 20rpx;
        z-index: 10;
    }

    &__camera-circle {
        width: 140rpx;
        height: 140rpx;
        background: linear-gradient(135deg, rgba(0, 0, 0, 0.8) 0%, rgba(60, 60, 60, 0.8) 100%);
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        box-shadow: 0 6rpx 20rpx rgba(0, 0, 0, 0.4);
        border: 4rpx solid rgba(255, 255, 255, 0.3);

        &:active {
            transform: scale(0.95);
            transition: transform 0.1s ease;
        }
    }

    &__photo-text {
        font-size: 32rpx;
        color: #333333;
        font-weight: bold;
        text-shadow: 0 2rpx 4rpx rgba(255, 255, 255, 0.8);
        background: rgba(255, 255, 255, 0.9);
        padding: 8rpx 16rpx;
        border-radius: 20rpx;
        border: 2rpx solid #E6D35A;
    }
}

/* 适配不同屏幕尺寸 */
@media screen and (max-width: 375px) {
    .business-license-print {
        &__license-container {
            margin: 15rpx;
            padding: 30rpx 20rpx;
            min-height: 700rpx;
        }

        &__title-text {
            font-size: 40rpx;
        }

        &__camera-circle {
            width: 100rpx;
            height: 100rpx;
        }

        &__photo-text {
            font-size: 28rpx;
        }

        &__cell {
            height: 45rpx;
        }
    }
}
</style>
