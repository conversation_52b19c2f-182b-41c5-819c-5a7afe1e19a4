<template>
    <f-page-layout 
        title="驾驶证/行驶证拍照打印" 
        fallback-path="/pages/copy/index"
        @back-click="onBackClick"
    >
        <view class="driving-license-print">
            <!-- 顶部提示 -->
            <view class="driving-license-print__header">
                <text class="driving-license-print__tip">内容拍照时需要与背景颜色尽量区分</text>
            </view>

            <view class="driving-license-print__content">
                <!-- 驾驶证主页拍摄区域 -->
                <view class="driving-license-print__card-container" @click="showActionSheet('front')">
                    <view class="driving-license-print__card driving-license-print__card--front">
                        <!-- 驾驶证主页背景图片 -->
                        <image
                            v-if="frontCardImage"
                            class="driving-license-print__card-image"
                            :src="frontCardImage"
                            mode="aspectFill"
                        />
                        <!-- 无图片时的占位符 -->
                        <view v-else class="driving-license-print__card-placeholder">
                            <view class="driving-license-print__placeholder-text">驾驶证主页模板</view>
                            <view class="driving-license-print__placeholder-desc">图片将在此处显示</view>
                        </view>

                        <!-- 相机图标覆盖层 -->
                        <view class="driving-license-print__camera-overlay">
                            <view class="driving-license-print__camera-icon">
                                <uni-icons type="camera" color="#FFFFFF" size="40"></uni-icons>
                            </view>
                            <view class="driving-license-print__camera-text">
                                拍摄主页
                            </view>
                        </view>
                    </view>
                </view>

                <!-- 驾驶证副页拍摄区域 -->
                <view class="driving-license-print__card-container" @click="showActionSheet('back')">
                    <view class="driving-license-print__card driving-license-print__card--back">
                        <!-- 驾驶证副页背景图片 -->
                        <image
                            v-if="backCardImage"
                            class="driving-license-print__card-image"
                            :src="backCardImage"
                            mode="aspectFill"
                        />
                        <!-- 无图片时的占位符 -->
                        <view v-else class="driving-license-print__card-placeholder">
                            <view class="driving-license-print__placeholder-text">驾驶证副页模板</view>
                            <view class="driving-license-print__placeholder-desc">图片将在此处显示</view>
                        </view>

                        <!-- 相机图标覆盖层 -->
                        <view class="driving-license-print__camera-overlay">
                            <view class="driving-license-print__camera-icon">
                                <uni-icons type="camera" color="#FFFFFF" size="40"></uni-icons>
                            </view>
                            <view class="driving-license-print__camera-text">
                                拍摄副页
                            </view>
                        </view>
                    </view>
                </view>
            </view>

        </view>
    </f-page-layout>
</template>

<script>
export default {
    data() {
        return {
            currentSide: '', // 'front' 或 'back'
            frontPhoto: '', // 用户拍摄的主页照片
            backPhoto: '', // 用户拍摄的副页照片
            // 驾驶证模板图片 - 留空供后续引用
            frontCardImage: '', // 主页模板图片路径
            backCardImage: '' // 副页模板图片路径
        };
    },
    methods: {
        // 返回上一页
        onBackClick() {
            uni.navigateBack();
        },
        
        // 显示操作选择
        showActionSheet(side) {
            this.currentSide = side;
            const sideText = side === 'front' ? '主页' : '副页';

            uni.showActionSheet({
                itemList: ['拍照', '从相册选择'],
                success: (res) => {
                    if (res.tapIndex === 0) {
                        this.takePhoto();
                    } else if (res.tapIndex === 1) {
                        this.chooseFromAlbum();
                    }
                }
            });
        },

        // 拍照
        takePhoto() {
            uni.chooseImage({
                count: 1,
                sourceType: ['camera'],
                success: (res) => {
                    this.handleImageSelected(res.tempFilePaths[0]);
                },
                fail: (err) => {
                    console.error('拍照失败:', err);
                    uni.showToast({
                        title: '拍照失败',
                        icon: 'none'
                    });
                }
            });
        },

        // 从相册选择
        chooseFromAlbum() {
            uni.chooseImage({
                count: 1,
                sourceType: ['album'],
                success: (res) => {
                    this.handleImageSelected(res.tempFilePaths[0]);
                },
                fail: (err) => {
                    console.error('选择图片失败:', err);
                    uni.showToast({
                        title: '选择图片失败',
                        icon: 'none'
                    });
                }
            });
        },

        // 处理选中的图片
        handleImageSelected(imagePath) {
            if (this.currentSide === 'front') {
                this.frontPhoto = imagePath;
            } else {
                this.backPhoto = imagePath;
            }

            const sideText = this.currentSide === 'front' ? '主页' : '副页';

            // 检查是否两面都已拍摄
            if (this.frontPhoto && this.backPhoto) {
                this.proceedToPrintSettings();
            } else {
                uni.showToast({
                    title: `${sideText}拍摄完成`,
                    icon: 'success'
                });
            }
        },

        // 跳转到打印设置
        proceedToPrintSettings() {
            uni.showModal({
                title: '拍摄完成',
                content: '驾驶证/行驶证正反面已拍摄完成，是否进入打印设置？',
                success: (res) => {
                    if (res.confirm) {
                        this.confirmPrint();
                    }
                }
            });
        },
        
        // 确认打印
        confirmPrint() {
            if (!this.frontPhoto || !this.backPhoto) {
                uni.showToast({
                    title: '请先拍摄主页和副页',
                    icon: 'none'
                });
                return;
            }

            // 跳转到打印设置页面，传递照片数据
            const photoData = encodeURIComponent(JSON.stringify({
                front: this.frontPhoto,
                back: this.backPhoto
            }));
            uni.navigateTo({
				
            });
        },

        // 设置驾驶证模板图片 - 供后续引用
        setDrivingLicenseTemplateImages(frontImageUrl, backImageUrl) {
            this.frontCardImage = frontImageUrl;
            this.backCardImage = backImageUrl;
        }
    },

    // 页面加载时的处理
    onLoad() {
        // 这里可以设置默认的驾驶证模板图片
        // 示例：this.setDrivingLicenseTemplateImages('/static/images/driving-license-front.png', '/static/images/driving-license-back.png');
    }
};
</script>

<style lang="scss">
.driving-license-print {
    padding: 20rpx;

    &__header {
        background: linear-gradient(135deg, #FFE4B5 0%, #FFF8DC 100%);
        padding: 20rpx;
        border-radius: 12rpx;
        margin-bottom: 30rpx;
        text-align: center;
    }

    &__tip {
        font-size: 28rpx;
        color: #D2691E;
        font-weight: 500;
    }

    &__content {
        display: flex;
        flex-direction: column;
        gap: 30rpx;
    }

    &__card-container {
        position: relative;

        &:active {
            opacity: 0.9;
        }
    }

    &__card {
        position: relative;
        width: 100%;
        height: 400rpx;
        border-radius: 20rpx;
        overflow: hidden;
        box-shadow: 0 8rpx 24rpx rgba(0, 0, 0, 0.15);
    }

    &__card-image {
        width: 100%;
        height: 100%;
        position: absolute;
        top: 0;
        left: 0;
        border-radius: 20rpx;
        object-fit: cover;
    }

    &__card-placeholder {
        width: 100%;
        height: 100%;
        position: absolute;
        top: 0;
        left: 0;
        border-radius: 20rpx;
        background: linear-gradient(135deg, #F5F5F5 0%, #E8E8E8 100%);
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        border: 2rpx dashed #CCCCCC;
        box-sizing: border-box;
    }

    &__placeholder-text {
        font-size: 28rpx;
        color: #999999;
        font-weight: 500;
        margin-bottom: 8rpx;
    }

    &__placeholder-desc {
        font-size: 24rpx;
        color: #BBBBBB;
    }

    // 相机覆盖层样式
    &__camera-overlay {
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: rgba(0, 0, 0, 0.4);
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        gap: 20rpx;
        border-radius: 20rpx;
    }

    &__camera-icon {
        width: 120rpx;
        height: 120rpx;
        background-color: rgba(0, 0, 0, 0.6);
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
    }

    &__camera-text {
        font-size: 32rpx;
        color: #FFFFFF;
        font-weight: 500;
        text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.5);
        text-align: center;
    }
}

/* 适配不同屏幕尺寸 */
@media screen and (max-width: 375px) {
    .driving-license-print {
        padding: 15rpx;

        &__card {
            height: 360rpx;
        }

        &__camera-icon {
            width: 100rpx;
            height: 100rpx;
        }

        &__camera-text {
            font-size: 28rpx;
        }

        &__placeholder-text {
            font-size: 24rpx;
        }

        &__placeholder-desc {
            font-size: 20rpx;
        }
    }
}
</style>
