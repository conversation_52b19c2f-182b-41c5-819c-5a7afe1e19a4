{"version": 3, "file": "photo-print.js", "sources": ["pages/document/photo-print.vue", "C:/Program Files/HBuilderX.4.66.2025051912/plugins/uniapp-cli-vite/uniPage:/cGFnZXMvZG9jdW1lbnQvcGhvdG8tcHJpbnQudnVl"], "sourcesContent": ["<template>\n\t<f-page-layout \n\t\ttitle=\"拍照打印\" \n\t\tfallback-path=\"/pages/document/index\"\n\t\t@back-click=\"onBackClick\"\n\t>\n\t\t<view class=\"photo-print\">\n\t\t\t<view class=\"photo-print__container\">\n\t\t\t\t<!-- 第一行：A4文档 和 身份证/居住证 -->\n\t\t\t\t<view class=\"photo-print__row\">\n\t\t\t\t\t<!-- A4文档 -->\n\t\t\t\t\t<view class=\"photo-print__card\" @click=\"handleA4Print\">\n\t\t\t\t\t\t<view class=\"photo-print__card-icon photo-print__card-icon--a4\">\n\t\t\t\t\t\t\t<uni-icons type=\"paperclip\" color=\"#FFFFFF\" size=\"22\"></uni-icons>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t\t<view class=\"photo-print__card-text\">\n\t\t\t\t\t\t\t<text class=\"photo-print__card-title\">A4文档</text>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</view>\n\t\t\t\t\t\n\t\t\t\t\t<!-- 身份证/居住证 -->\n\t\t\t\t\t<view class=\"photo-print__card\" @click=\"handleIdCardPrint\">\n\t\t\t\t\t\t<view class=\"photo-print__card-icon photo-print__card-icon--id\">\n\t\t\t\t\t\t\t<uni-icons type=\"person\" color=\"#FFFFFF\" size=\"22\"></uni-icons>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t\t<view class=\"photo-print__card-text\">\n\t\t\t\t\t\t\t<view>\n\t\t\t\t\t\t\t\t<text class=\"photo-print__card-title\">身份证</text>\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t<view>\n\t\t\t\t\t\t\t\t<text class=\"photo-print__card-title\">居住证</text>\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t\t\n\t\t\t\t<!-- 第二行：户口本 和 营业执照 -->\n\t\t\t\t<view class=\"photo-print__row\">\n\t\t\t\t\t<!-- 户口本 -->\n\t\t\t\t\t<view class=\"photo-print__card\" @click=\"handleHouseholdPrint\">\n\t\t\t\t\t\t<view class=\"photo-print__card-icon photo-print__card-icon--household\">\n\t\t\t\t\t\t\t<uni-icons type=\"list\" color=\"#FFFFFF\" size=\"22\"></uni-icons>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t\t<view class=\"photo-print__card-text\">\n\t\t\t\t\t\t\t<text class=\"photo-print__card-title\">户口本</text>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</view>\n\t\t\t\t\t\n\t\t\t\t\t<!-- 营业执照 -->\n\t\t\t\t\t<view class=\"photo-print__card\" @click=\"handleBusinessLicensePrint\">\n\t\t\t\t\t\t<view class=\"photo-print__card-icon photo-print__card-icon--business\">\n\t\t\t\t\t\t\t<uni-icons type=\"shop\" color=\"#FFFFFF\" size=\"22\"></uni-icons>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t\t<view class=\"photo-print__card-text\">\n\t\t\t\t\t\t\t<text class=\"photo-print__card-title\">营业执照</text>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t\t\n\t\t\t\t<!-- 第三行：驾驶证/行驶证 和 银行卡/社保卡 -->\n\t\t\t\t<view class=\"photo-print__row\">\n\t\t\t\t\t<!-- 驾驶证/行驶证 -->\n\t\t\t\t\t<view class=\"photo-print__card\" @click=\"handleDrivingLicensePrint\">\n\t\t\t\t\t\t<view class=\"photo-print__card-icon photo-print__card-icon--driving\">\n\t\t\t\t\t\t\t<uni-icons type=\"navigate\" color=\"#FFFFFF\" size=\"22\"></uni-icons>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t\t<view class=\"photo-print__card-text\">\n\t\t\t\t\t\t\t<view>\n\t\t\t\t\t\t\t\t<text class=\"photo-print__card-title\">驾驶证</text>\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t<view>\n\t\t\t\t\t\t\t\t<text class=\"photo-print__card-title\">行驶证</text>\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</view>\n\t\t\t\t\t\n\t\t\t\t\t<!-- 银行卡/社保卡 -->\n\t\t\t\t\t<view class=\"photo-print__card\" @click=\"handleBankCardPrint\">\n\t\t\t\t\t\t<view class=\"photo-print__card-icon photo-print__card-icon--card\">\n\t\t\t\t\t\t\t<uni-icons type=\"wallet\" color=\"#FFFFFF\" size=\"22\"></uni-icons>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t\t<view class=\"photo-print__card-text\">\n\t\t\t\t\t\t\t<view>\n\t\t\t\t\t\t\t\t<text class=\"photo-print__card-title\">银行卡</text>\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t<view>\n\t\t\t\t\t\t\t\t<text class=\"photo-print__card-title\">社保卡</text>\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t</view>\n\t</f-page-layout>\n</template>\n\n<script>\n\texport default {\n\t\tdata() {\n\t\t\treturn {\n\t\t\t\t// 数据\n\t\t\t};\n\t\t},\n\t\tmethods: {\n\t\t\t// 返回上一页\n\t\t\tonBackClick() {\n\t\t\t\tconsole.log('返回上一页');\n\t\t\t},\n\t\t\t// 处理A4文档打印\n\t\t\thandleA4Print() {\n\t\t\t\tconsole.log('点击了A4文档');\n\t\t\t\t// 跳转到复印服务的A4文档打印页面\n\t\t\t\tuni.navigateTo({\n\t\t\t\t\turl: '/pages/copy/a4-print'\n\t\t\t\t});\n\t\t\t},\n\t\t\t// 处理身份证/居住证打印\n\t\t\thandleIdCardPrint() {\n\t\t\t\tconsole.log('点击了身份证/居住证');\n\t\t\t\tuni.navigateTo({\n\t\t\t\t\turl: '/pages/copy/id-card-print'\n\t\t\t\t});\n\t\t\t},\n\t\t\t// 处理户口本打印\n\t\t\thandleHouseholdPrint() {\n\t\t\t\tconsole.log('点击了户口本');\n\t\t\t\tuni.navigateTo({\n\t\t\t\t\turl: '/pages/copy/household-print'\n\t\t\t\t});\n\t\t\t},\n\t\t\t// 处理营业执照打印\n\t\t\thandleBusinessLicensePrint() {\n\t\t\t\tconsole.log('点击了营业执照');\n\t\t\t\tuni.navigateTo({\n\t\t\t\t\turl: '/pages/copy/business-license-print'\n\t\t\t\t});\n\t\t\t},\n\t\t\t// 处理驾驶证/行驶证打印\n\t\t\thandleDrivingLicensePrint() {\n\t\t\t\tconsole.log('点击了驾驶证/行驶证');\n\t\t\t\tuni.navigateTo({\n\t\t\t\t\turl: '/pages/copy/driving-license-print'\n\t\t\t\t});\n\t\t\t},\n\t\t\t// 处理银行卡/社保卡打印\n\t\t\thandleBankCardPrint() {\n\t\t\t\tconsole.log('点击了银行卡/社保卡');\n\t\t\t\tuni.navigateTo({\n\t\t\t\t\turl: '/pages/copy/bank-card-print'\n\t\t\t\t});\n\t\t\t}\n\t\t}\n\t}\n</script>\n\n<style lang=\"scss\">\n\t.photo-print {\n\t\tbackground-color: #f5f5f5;\n\t\tmin-height: 100%;\n\t\tpadding: 20rpx;\n\t\t\n\t\t&__container {\n\t\t\tdisplay: flex;\n\t\t\tflex-direction: column;\n\t\t\tgap: 20rpx;\n\t\t}\n\t\t\n\t\t&__row {\n\t\t\tdisplay: flex;\n\t\t\tgap: 20rpx;\n\t\t}\n\t\t\n\t\t&__card {\n\t\t\tflex: 1;\n\t\t\tbackground-color: #ffffff;\n\t\t\tborder-radius: 12rpx;\n\t\t\tdisplay: flex;\n\t\t\tflex-direction: row;\n\t\t\talign-items: center;\n\t\t\tpadding: 25rpx 20rpx;\n\t\t\tbox-shadow: 0 1rpx 4rpx rgba(0, 0, 0, 0.05);\n\t\t\tmin-height: 80rpx;\n\t\t}\n\t\t\n\t\t&__card-icon {\n\t\t\twidth: 70rpx;\n\t\t\theight: 70rpx;\n\t\t\tborder-radius: 10rpx;\n\t\t\tdisplay: flex;\n\t\t\talign-items: center;\n\t\t\tjustify-content: center;\n\t\t\tmargin-right: 25rpx;\n\t\t\tflex-shrink: 0;\n\t\t\t\n\t\t\t&--a4 {\n\t\t\t\tbackground-color: #ff9800;\n\t\t\t}\n\t\t\t\n\t\t\t&--id {\n\t\t\t\tbackground-color: #3f51b5;\n\t\t\t}\n\t\t\t\n\t\t\t&--household {\n\t\t\t\tbackground-color: #e53935;\n\t\t\t}\n\t\t\t\n\t\t\t&--business {\n\t\t\t\tbackground-color: #4caf50;\n\t\t\t}\n\t\t\t\n\t\t\t&--driving {\n\t\t\t\tbackground-color: #2196f3;\n\t\t\t}\n\t\t\t\n\t\t\t&--card {\n\t\t\t\tbackground-color: #e91e63;\n\t\t\t}\n\t\t}\n\t\t\n\t\t&__card-text {\n\t\t\tdisplay: flex;\n\t\t\tflex-direction: column;\n\t\t\tflex: 1;\n\t\t\tjustify-content: center;\n\t\t\t\n\t\t\tview {\n\t\t\t\tmargin: 2rpx 0;\n\t\t\t}\n\t\t}\n\t\t\n\t\t&__card-title {\n\t\t\tfont-size: 30rpx;\n\t\t\tcolor: #333;\n\t\t\tfont-weight: bold;\n\t\t\tline-height: 1.4;\n\t\t}\n\t}\n</style> \n", "import MiniProgramPage from 'D:/Yunchuang/PhotoFront/fotoBox-front/pages/document/photo-print.vue'\nwx.createPage(MiniProgramPage)"], "names": ["uni"], "mappings": ";;AAiGC,MAAK,YAAU;AAAA,EACd,OAAO;AACN,WAAO;AAAA;AAAA;EAGP;AAAA,EACD,SAAS;AAAA;AAAA,IAER,cAAc;AACbA,oBAAAA,MAAA,MAAA,OAAA,yCAAY,OAAO;AAAA,IACnB;AAAA;AAAA,IAED,gBAAgB;AACfA,oBAAAA,MAAA,MAAA,OAAA,yCAAY,SAAS;AAErBA,oBAAAA,MAAI,WAAW;AAAA,QACd,KAAK;AAAA,MACN,CAAC;AAAA,IACD;AAAA;AAAA,IAED,oBAAoB;AACnBA,oBAAAA,MAAY,MAAA,OAAA,yCAAA,YAAY;AACxBA,oBAAAA,MAAI,WAAW;AAAA,QACd,KAAK;AAAA,MACN,CAAC;AAAA,IACD;AAAA;AAAA,IAED,uBAAuB;AACtBA,oBAAAA,MAAA,MAAA,OAAA,yCAAY,QAAQ;AACpBA,oBAAAA,MAAI,WAAW;AAAA,QACd,KAAK;AAAA,MACN,CAAC;AAAA,IACD;AAAA;AAAA,IAED,6BAA6B;AAC5BA,oBAAAA,MAAA,MAAA,OAAA,yCAAY,SAAS;AACrBA,oBAAAA,MAAI,WAAW;AAAA,QACd,KAAK;AAAA,MACN,CAAC;AAAA,IACD;AAAA;AAAA,IAED,4BAA4B;AAC3BA,oBAAAA,MAAY,MAAA,OAAA,yCAAA,YAAY;AACxBA,oBAAAA,MAAI,WAAW;AAAA,QACd,KAAK;AAAA,MACN,CAAC;AAAA,IACD;AAAA;AAAA,IAED,sBAAsB;AACrBA,oBAAAA,MAAY,MAAA,OAAA,yCAAA,YAAY;AACxBA,oBAAAA,MAAI,WAAW;AAAA,QACd,KAAK;AAAA,MACN,CAAC;AAAA,IACF;AAAA,EACD;AACD;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACvJD,GAAG,WAAW,eAAe;"}